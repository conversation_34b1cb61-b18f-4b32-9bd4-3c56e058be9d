﻿
Fly booking requirements

History of documents

|**Version**|**Content**|**Author**|**Reviewed by**|
| :- | :- | :- | :- |
|**1.0**||||
|**1.1**||||
1. # **Work flow of Fly Booking**



**SEARCH: <PERSON>ra cứu theo các tiêu chí:**

**Điểm đi - <PERSON>iểm đến**

**M<PERSON>t chiều/ Hai chiều**

**ngày đi/ ngày về**

**Số lượng người lớn >12t,** 

` `**trẻ >4t, trẻ 2t-4t**



**Search results**

**Non-stop/ Transit**

**Refundable**

**Airlines**



**DETAILS:**

**Nhập thông tin khách hàng / Login** 

**Nhập thông tin hành khách**

**Coupon**



**CONFIRM: Xem tổng hợp thông tin, invoice và xác nhận**



**PAYMENT: <PERSON><PERSON><PERSON> chọn:**

**- Nhập thanh toán**

**- <PERSON><PERSON> <PERSON><PERSON> khi đến**
![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.001.png)
1. # **Mockup and Specification of Fly Booking**
   1. ## **SEARCH:** 
      1. ### **Link tham khảo : https://www.phptravels.net/**
      1. ### **Mockup:  Màn hình tra cứu chuyến bay**
![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.002.png)

|**#**|**Trường**|**Require**|**Mô tả**|
| :- | :- | :- | :- |
|**A**|**Các điều kiện tra cứu**|||
|**1**|Nơi đi|X|<p>- Nhận kí hiệu nơi đi, ví dụ HAN, hệ thống sẽ hiển thị nơi đi gồm Tên, mã. Ví dụ: Noi Bai airport (HAN)</p><p>- Chỉ hiển thị gợi ý (hint) khi gõ keyword từ 3 ký tự trở lên </p>|
|**2**|Nơi đến|X|<p>- Nhận kí hiệu nơi đến, ví dụ SGN, hệ thống sẽ hiển thị nơi đến gồm Tên, mã. Ví dụ: Tan Son Nhat airport (HAN)</p><p>- Chỉ hiển thị gợi ý (hint) khi gõ keyword từ 3 ký tự trở lên</p><p>- Nơi đi và Nơi đến phải khác nhau </p>|
|**3**|Ngày đi|x|Chỉ cho phép chọn ngày đi từ calendar|
|**4**|Ngày về||<p>Nếu người dùng chọn Một chiều (one way) thì chỉ chọn được ngày đi, ngày về bị ẩn</p><p>Nếu người dùng chọn Khứ hồi (Round trip) thì hiển thị ngày về và bắt buộc nhập bằng cách chọn ngày đi trên calendar</p>|
|**5**|Số lượng người lớn >12t|x|<p>Default=1</p><p>Phải nhập số lượng tối thiểu = 1, tối đa là 3</p><p>Cần cấu hình min = 1, max=3 để có thể thay đổi được</p>|
|**6**|Số lượng trẻ >=4t||Có thể không cần nhập, mặc định = 0|
|**7**|Số lượng trẻ < 4t||Có thể không cần nhập, mặc định = 0|
|**Lưu ý**|Tổng số ghế||<p>Tổng số ghế người lớn >= tổng số trẻ em</p><p>( Một người lớn chỉ được phép đi cùng với 1 trẻ em )</p>|
|**8**|Một chiều/ Khứ hồi|X|<p>Mặc định là option Một chiều (one way)</p><p>Nếu chọn Khứ hồi (Round trip) thì hiển thị thêm Ngày về</p>|
|**9**|Hạng |X|Mặc định là Economy. Được phép chọn khác ( Economy, Business, First )|
|**B**|**Button “Search”**||<p>Người dùng nhấn nút Search, hệ thống kiểm tra tính đúng đắn đầy đủ của dữ liệu:</p><p>- Báo lỗi “Xin hãy thông tin/ Please fulfill the input” nếu các trường bắt buộc bị thiếu thông tin (Nơi đi, nơi đến, ngày đi, số lượng người nếu =0)</p><p>- Nếu chọn Round trip, thì bắt buộc thêm trường ngày về</p><p>- Báo lỗi “Ngày về phải sau ngày đi” nếu trường ngày về <= ngày đi</p><p>Thông tin đầu vào đầy đủ, hợp lệ, hệ thống thực hiện tra cứu và hiển thị danh sách các chuyến bay của các hãng bay, giá vé cho các chuyến bay (Form FILTER)</p>|
1. ## **Search results :** 
   1. ### **Link tham khảo :**
   1. ### **Mockup:  Màn hình danh sách chuyến bay**
![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.003.png)

|**#**|**Trường**|**Require**|**Mô tả**|
| :- | :- | :- | :- |
||<p>**Các options ban đều load default là uncheck**</p><p>**Khi check 1 hoặc 1 vài điều kiện, thì tổ hợp theo điều kiện AND để hiển thị danh sách**</p>|||
|**A**|**Các điều kiện tra cứu**||<p>Hiển thị các dữ liệu mà ng dùng đã tra cứu</p><p>Người dùng có thể thay đổi điều kiện tra cứu để Search lượt mới</p>|
|**B**|**Nút “Search”**||<p>Nút search sẽ cập nhật kết quả seach cho phần </p><p>**C. Date Availability** và **E. Available Flights**</p>|
|**C**|<h5>- <a name="_heading=h.urmj4740knp3"></a>**Filter by [ROUTE STOPS]**</h5>||NGười dùng có thể lọc tìm những chuyến bay thẳng hoặc những chuyến bay có trạm nghỉ|
|**D**|**Filter by [airlines]**||Hiển thị toàn bộ các hãng máy bay. Nếu người dùng chọn 1 hoặc 1 số các hãng bay, thì hệ thống tự động lọc các chuyến bay theo các hãng vừa chọn|
|**E**|**Available Flights**|||
|**1**|**Icon hãng bay, Tên hãng bay**||Hiển thị icon của hãng máy bay|
|**2**|**Mã số chuyến bay**||Mã số chuyến bay (ví dụ: 504)|
|**3**|**Thông tin đường bay**||Mã nơi đi, nơi đến. Ví dụ: HAN ⭢ SGN|
|**4**|**Thời gian khỏi hành**||Hiển thị Ngày, giờ bay|
|**5**|**Thời gian đến** ||Hiển thị ngày,giờ đến điểm đến|
|**6**|**Đặc điểm chuyến bay non-stop/transit**||Hiển thị Đặc điểm chuyến bay non-stop hoặc transit|
|**7**|**Hiển thị Giá vé**||Đơn vị tính USD|
|**8**|**Button [Book Now]**||Luôn luôn enable,click vào button này sẽ di chuyển đến màn hình nhập thông tin khách hàng|

1. ## **DETAILS: Nhập thông tin khách hàng  (Personal Details)**
- Tại màn hình Personal Details , user phải điền những thông tin cần thiết rồi click button [Confirm This Booking ] để đến màn hình lập hóa đơn “Invoice”.
- Max lenght cho các field = 50 characters . Nếu để trống trường bắt buộc thì hiển thị error message dưới textbox bị lỗi  : This field is required 

<table><tr><th colspan="1" valign="top"><b>#</b></th><th colspan="1" valign="top"><b>Trường</b></th><th colspan="1" valign="top"><b>Require</b></th><th colspan="1" valign="top"><b>Mô tả</b></th></tr>
<tr><td colspan="1" valign="top"></td><td colspan="1" valign="top"><b>Personal Details</b></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="1" valign="top"><b>1</b></td><td colspan="1" valign="top">First Name</td><td colspan="1" valign="top">x</td><td colspan="1" rowspan="3" valign="top">là những trường bắt buộc phải nhập , nếu để trống sẽ hiển thị error message : This field is required , max = 50 lý tự</td></tr>
<tr><td colspan="1" valign="top"><b>2</b></td><td colspan="1" valign="top">Last Name</td><td colspan="1" valign="top">x</td></tr>
<tr><td colspan="1" valign="top"><b>3</b></td><td colspan="1" valign="top">Email</td><td colspan="1" valign="top">x</td></tr>
<tr><td colspan="1" valign="top"><b>4</b></td><td colspan="1" valign="top">Mobile </td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Maxlength cho Mobile là 16 chỉ cho phép nhập vào là number </td></tr>
<tr><td colspan="1" valign="top"><b>5</b></td><td colspan="1" valign="top">Address</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Maxlength cho tất cả các trường còn lại trên form  này là 50 characters</td></tr>
<tr><td colspan="1" valign="top"><b>6</b></td><td colspan="1" valign="top">Country</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Click vào field “Country” hiển thị danh sách các nước có trong hệ thống</p><p>User  có thể search country  ở item  Country</p></td></tr>
<tr><td colspan="1" valign="top"><b>7</b></td><td colspan="1" valign="top">Coupon</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>User  có thể sử dụng mã giảm giá bắng cách nhập coupon Code hợp lệ vào trường [Conpon Code] rồi click [Apply Coupon] button  để hưởng những ưu đãi giảm giá</p><p>Nếu nhập mã giảm giá ,mà không click vào [Apply Coupon] thì mã coupon đó coi như không nhập</p></td></tr>
<tr><td colspan="1" valign="top"></td><td colspan="1" valign="top"><b>Passengers</b></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Nhập thông tin hành khách tham gia chuyến bay.</p><p><b>Lưu ý</b>: các thông tin hành khách tương ứng với số lượng hành khách đã search.</p><p>Cần kiểm soát dữ liệu nhập ví dụ như: Khách hàng đặt 1 khách trên 12 tuổi, 1 khách từ 4-12 tuổi, và 1 khách dưới 4 tuổi, thì tương ứng giao diện phải hiển thị 3 khung nhập.</p><p>- Adult : form nền trắng</p><p>- Child : form nền xanh</p><p>- Infant: form nền hồng</p><p>Khi nhập xong hệ thống phải kiểm tra phải đủ 3 passenger và tuổi phải nằm trong khoảng đã nhập search ở trên.</p></td></tr>
<tr><td colspan="1" valign="top"><b>8</b></td><td colspan="1" valign="top">Name</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">Nhập tên, chỉ gồm các kí tự Unicode</td></tr>
<tr><td colspan="1" valign="top"><b>9</b></td><td colspan="1" valign="top">Age</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top"><p>Click vào hiển thị đúng số tuổi phải nằm trong khoảng đã đăng ký ở trên </p><p>Vidu : nếu click vào Age ở form nền hồng thì chỉ hiển thị số tuổi từ : < 4 ,3,2,1,0</p></td></tr>
<tr><td colspan="1" valign="top"><b>10</b></td><td colspan="1" valign="top">Passport Number</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">Nhập số, chữ, không gồm các kí tự đặc biệt, không gồm dấu cách</td></tr>
<tr><td colspan="1" valign="top"></td><td colspan="1" valign="top"><b>Coupon</b></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="1" valign="top"><b>11</b></td><td colspan="1" valign="top">Coupon number</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Nhập số counpon, nhấn Apply, hệ thống sẽ kiểm tra valid của coupon ,nếu đúng thì sẽ giảm giá theo giá trị của coupon đó</td></tr>
<tr><td colspan="1" valign="top"></td><td colspan="1" valign="top"><b>Nhấn nút [Confirm This Booking ]</b></td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Xác nhận thông tin đăng ký</td></tr>
<tr><td colspan="1" valign="top"></td><td colspan="1" valign="top"><b>Booking Summary</b></td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Hiển thị thông tin chuyến bay</td></tr>
<tr><td colspan="1" valign="top"><b>12</b></td><td colspan="1" valign="top">Departure date From HAN2019-03-04</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Hiển thị ngày khởi hành, cấu trúc:</p><p>Departure date From <nơi đi> <ngày đi></p></td></tr>
<tr><td colspan="1" valign="top"><b>13</b></td><td colspan="1" valign="top">Arrival date at SGN2019-03-05</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Hiển thị ngày khởi hành, cấu trúc:</p><p>Arrival date at <nơi đến> <ngày đến></p></td></tr>
<tr><td colspan="1" valign="top"><b>14</b></td><td colspan="1" valign="top">Departure time from HAN20:58:00</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Hiển thị thời gian khởi hành, cấu trúc:</p><p>Departure time From <nơi đi> <ngày đi></p></td></tr>
<tr><td colspan="1" valign="top"><b>15</b></td><td colspan="1" valign="top">Arrival Time at SGN20:58:00</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Hiển thị thời gian khởi hành, cấu trúc:</p><p>Arrival time at <nơi đến> <ngày đến></p></td></tr>
<tr><td colspan="1" valign="top"><b>16</b></td><td colspan="1" valign="top">Total Travelling Hours7</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Hiển thị tổng thời gian di chuyển</td></tr>
<tr><td colspan="1" valign="top"><b>17</b></td><td colspan="1" valign="top"><p>Adults 1</p><p>Childs 0</p><p>Infacts 0</p></td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Hiển thị số lượng người đã đăng ký</td></tr>
<tr><td colspan="1" valign="top"><b>18</b></td><td colspan="1" valign="top">Deposit Now	USD 289.4688$</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Hiển thị giá vé</td></tr>
<tr><td colspan="1" valign="top"><b>19</b></td><td colspan="1" valign="top">Tax & VAT	USD 209.76$</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">= 10% Hiển thị giá vé</td></tr>
<tr><td colspan="1" valign="top"><b>20</b></td><td colspan="1" valign="top">Total Amount	USD 971.52$</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">Tổng tiền = Tax & VAT + Deposit Now</td></tr>
<tr><td colspan="1" valign="top"><b>21</b></td><td colspan="1" valign="top"><p>NEED HELP?</p><p>24/7 Customer Support</p><p>` `+92-3311442244</p><p><EMAIL></p></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"><p>Thông tin hỗ trợ: Điện thoại, email</p><p></p><p></p><p></p></td></tr>
</table>

**Note** : Có bao nhiêu người được select ở màn search fly  thì ở Form Passengers sẽ hiển thị tương ứng đúng số form = số người đã chọn ở màn search fly 

- Form Passengers màu trắng : nhập thông tin cho Adult ( field Age chỉ cho phép nhập số tuổi từ 12-> 100  )
- Form Passengers màu xanh : nhập thông tin cho Child  ( field child  chỉ cho phép nhập số tuổi từ 4->11 tuổi  )

- Form Passengers màu hồng : nhập thông tin cho Infant  ( field infant  chỉ cho phép nhập số tuổi từ 0->3 tuổi )

![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.004.png)



<a name="_heading=h.gjdgxs"></a> 


1. ## **CONFIRM: Xem tổng hợp, invoice và xác nhận đặt phòng** 
   1. ### **Booking Summary**
Hiển thị tổng hợp thông tin của phần booking

|#|Thông tin|Mô tả|
| :-: | :-: | :- |
||0 Days 23 Hours 52 Minutes 11 Seconds|<p>Thời gian số ngày, số giờ, số phút, số giây **còn lại phải thanh toán (đếm ngược)**</p><p>**Chú ý: hết thời gian cần thanh toán mà vẫn chưa có giao dịch thanh toán thành công trên hệ thống, hệ thống sẽ tự động hủy book vé này**</p>|
|12|UNPAID|Hiển thị trạng thái invoice là unpaid (chưa thanh toán)|
|23|Invoice Date |Hiển thị ngày tạo invoice, format ddd/mm/yyyy|
|3|Due Date |Hiển thị Due date là ngày hết hạn thanh toán của invoice = ngày invoice + 1 tháng|
|4|Invoice number|Hiển thị Số invoice|
|5|Customer Details|<p>Hiển thị thông tin khách hàng theo từng dòng:</p><p>1. Fullname</p><p>2. Email </p><p>3. Address</p><p>4. Phone</p>|
|6|Depart|<p>Thông tin chuyến bay phần chiều đi:</p><p>Row 1: Date, Tên hãng máy bay, Loại (Oneway/Roundtrip), Hạng (economy/business/first)</p><p>Row 2: Hiển thị ngày giờ xuất phát từ Điểm đi, mã điểm đi (HAN), tên sân bay điểm đi (Noi Bai Airport)</p><p>Row 3: Hiển thị ngày giờ đến Điểm đến, mã điểm đến (SGN), tên sân bay điểm đến (Tan Son Nhat Airport)</p>|
|7|Return|Thông tin chuyến bay phần chiều về: Tương tự như phần chiều đi, với điều kiện lọc là thông tin của chiều về|
|8|<p>DEPOSIT NOW</p><p>TAX & VAT	</p><p>TOTAL AMOUNT</p>|<p>Hiển thị Số tiền trước thuế</p><p>Hiển thị Thuế, Vat (theo công thức)</p><p>Hiển thị Tổng số tiền</p>|
|9|NOTES / ADDITIONAL REQUESTS|Hiển thị phần ghi chú của khách hàng|
|10|Thông tin liên hệ hỗ trợ|<p>Tên công ty, địa chỉ, email, phone</p><p>PHPTRAVELS</p><p>` `1355 Market St, Suite 900 San Francisco, United States</p><p>` `<EMAIL></p><p>` `+92-3311442244</p>|
|11|Kết xuất thông tin|<p>Print : in thông tin booking từ trình duyệt</p><p>Download invoice : tải thông tin invoice về dạng file .png</p><p>Download PDF : Tải về dạng PDF</p><p><br>Yêu cầu : Hiển thị phần in từ trình duyệt, file png và pdf phải đẩy đủ thông tin của invoice, không xô lệch.</p>|
||Thanh Toán |<p>Nút để lựa chọn thanh toán khi đến sân bay (Pay arrive), hoặc thanh toán ngay (Pay now)</p><p></p>|
||Pay on arrival|<p>Nếu User lick chọn [PAY ON ARIVAL] hệ thống sẽ tự động hiển thị confirmation pop-up với nội dung : Are you sure you want to pay at arrival ? và 2 button [Ok] , [Cancel]</p><p>- Nếu Click vào button [Ok] , hệ thống sẽ hiển thị message </p><p>YOUR BOOKING STATUS IS RESERVED</p><p>YOU MUST CONFIRM YOUR BOOKING OTHERWISE IT WILL BE CANCELLED, PLEASE CONTACT US FOR FURTHER INFORMATION.</p><p></p><p></p><p>- Nếu click vào button [Cancel] >> pop-up được close đi . </p><p>Tình trạng thanh toán của invoice cũng được cập nhật tương ứng</p>|
||Pay now|<p>- Sau khi chọn Payment Method >> Hiển thị màn hình nhập thông tin của khách hàng để thanh toán</p><p>Khi nhấn **pay now**: hiển thị thêm phần Phương thức thanh toán (Select payment method) : Gồm các payment method : **bank transfer, Credit Card, Visa,Paypal**</p><p></p><p></p><p></p><p>1. Nếu chọn **bank transfer** thì hiển thị thông tin tài khoản của công ty</p><p>2. Nếu chọn các loại thanh toán từ ngân hàng như **credit card,Visa** thì hiển thị thông tin thanh toán của khách hàng (Họ, Tên, Số thẻ, tháng năm hết hạn, số CVV). Valid nếu nhập thiếu thông tin, tháng năm hết hạn < tháng năm hiện tại, Số thẻ không đủ 16 chữ số, số CVV không đủ 3 chữ số.</p><p>3. Nếu chọn thanh toán ví điện tử là **Paypal** thì hiển thị nút “Pay” để redirect đến trang thanh toán của đối tác</p><p>4. Nếu thanh toán bị lỗi thì hiển thị thông báo lỗi</p><p></p><p>**Nhập đầy đủ thông tin hợp lệ , click [PAY NOW] >> Thanh toán thành công đồng thời hiển thị message : Invoice [invoice\_number] is paid successfully**  </p><p></p><p>**Sau khi thanh toán thành công thì cập nhật trạng thái UNPAID 🡪 PAID**</p>|

![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.005.png)![](Aspose.Words.1d2455a6-44ff-486d-8c37-fd7df78d11c0.006.png)
`		 `11** / 11**

