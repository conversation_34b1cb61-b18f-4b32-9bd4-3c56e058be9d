Checklist Review Tài liệu Thiết kế chi tiết,,,,,
,,,,,
Mã dự án,,,,,
Phiên bản sản phẩm,,,,,
Lần xem xét,,,,,
Người xem xét,,,,,
<PERSON><PERSON>y xem xét,,,,,
Độ lớn sản phẩm,,,,,
Nguồn lực dành cho xem xét (MH),,,,,
"Hướng dẫn: Trong quá trình review nếu câu trả lời là ""có"" nhưng chưa thực sự đầy đủ thì đánh dấu vào cột ""Có"" và chú thích cụ thể vào cột ""Ghi chú""",,,,,
Câu hỏi,Có,Không,N/A,<PERSON><PERSON> chú,<PERSON>iều kiện
Kiểm soát tài liệu,,,,,
T<PERSON><PERSON> liệu có tuân thủ theo yêu cầu kiểm soát tài liệu không:,,,,,<PERSON><PERSON><PERSON> buộc
Tài liệu có tuân theo biểu mẫu mới nhất không?,,,,,
"Trang tiêu đề có bao gồm đủ tên tài liệu, phiên bản, tên công ty và logo, mã dự án, mã hiệu tài liệu và ngày ban hành không?",,,,,Bắt buộc
"Đầu và cuối trang có bao gồm tên, phiên bản của tài liệu không?",,,,,Bắt buộc
Đánh số trang có theo đúng qui định: số thứ tự/tổng số trang?,,,,,Bắt buộc
Trang ký có bao gồm đủ Người xem xét & Người phê duyệt không?,,,,,Bắt buộc
Có bảng theo dõi lịch sử thay đổi không? ,,,,,Bắt buộc
Thiết kế chi tiết chung,,,,,
Ngôn ngữ lập trình và công nghệ đã chọn có phù hợp không?,,,,,Bắt buộc
Các thông tin chi tiết có đủ để cho một người khác code không?,,,,,Bắt buộc
"Design có phản ánh môi trường thực tế không? Phần cứng, phần mềm?",,,,,Bắt buộc
Design có hỗ trợ việc modul hoá 1 cách thích hợp không?,,,,,
Design có hỗ trợ việc re-use design và code không?,,,,,
"Tất cả mọi ràng buộc như processing time, run time, memory usage, I/O, database access and response time có được phản ánh trong design không?",,,,,
Các giao tiếp nội bộ và giao tiếp với bên ngoài hệ thống có được xác định rõ ràng không?,,,,,Bắt buộc
Mọi giá trị đầu vào và đầu ra của các giao tiếp có đầy đủ và cần thiết không?,,,,,Bắt buộc
Thiết kế chi tiết hàm và thủ tục,,,,,
"Mỗi unit (class, method, property, form, file, etc.) đều có tên duy nhất và theo naming convention?",,,,,Bắt buộc
"Các lời gọi, events và message giữa các unit có được ghi rõ không?",,,,,Bắt buộc
Đối với mỗi unit có mô tả đầy đủ theo các yêu cầu sau không?,,,,,
"Chức năng, mục đích",,,,,Bắt buộc
Các tham số và giá trị trả về,,,,,Bắt buộc
Diagram flow,,,,,
Exception,,,,,Bắt buộc
"Có mô tả phương pháp xử lý lỗi (error, exception handling) trong ứng dụng không?",,,,,Bắt buộc
Có liệt kê các hàm và thủ tục xử lý lỗi không?,,,,,Bắt buộc
Thiết kế chi tiết cơ sở dữ liệu,,,,,
Các data elements có được mô tả đủ chi tiết không?,,,,,Bắt buộc
Miền cho phép của dữ liệu và các ràng buộc khác về dữ liệu có được mô tả không?,,,,,Bắt buộc
Việc quản lý và sử dụng các shared and stored data có được mô tả rõ không?,,,,,Bắt buộc
Có mô tả các mô hình quan hệ dữ liệu không?,,,,,Bắt buộc
Với mỗi bảng dữ liệu có mô tả chi tiết các thành phần sau không?,,,,,
Chi tiết các trường,,,,,Bắt buộc
Constraint,,,,,
Index,,,,,Bắt buộc
Trigger,,,,,
Có mô tả chi tiết thiết kế các tệp tin trao đổi với hệ thống khác hoặc kết xuất ra không?,,,,,Bắt buộc
Với mỗi tệp tin có mô tả chi tiết các thành phần sau không?,,,,,
Cấu trúc file theo thứ tự các trường,,,,,Bắt buộc
Chi tiết các trường,,,,,Bắt buộc
Có mô tả cách thiết kế các trường mã trong cơ sở dữ liệu không?,,,,,Bắt buộc
Có mô tả thiết kế vật lý của cơ sở dữ liệu không?,,,,,
Thiết kế chi tiết màn hình,,,,,
Có chức năng nào không có mô tả thiết kế chi tiết màn hình không?,,,,,
Có mô tả giao diện người dùng của hệ thống theo các yêu cầu sau không:,,,,,
Có mô tả/xác định vị trí của tất cả các thành phần trong giao diện người dùng không?,,,,,Bắt buộc
Có mô tả qui trình hoặc các hoạt động cho giao diện người dùng không?,,,,,Bắt buộc
Có mô tả việc đọc/ghi dữ liệu trong giao diện người dùng không?,,,,,Bắt buộc
Có mô tả quan hệ giữa các thành phần trong giao diện người dùng và các modul hoặc hàm dùng chung không?,,,,,
Có mô tả các thuật toán chính không?,,,,,
,,,,,
<Có thể thêm vào checklist các quan tâm khác nếu cần thiết>,,,,,
,,0,,0,
* Ý kiến,,,,,
,,,,,
,,,,,
,,,,,
,,,,,
* Đề xuất,,,,,
,,,,,
[X] - Đạt,,,,,
[   ] - Xem xét lại,,,,,
[   ] - Khác,,,,,
