Code Review Checklist,,,,
,,,,
Mã dự án:,,,,
Thành phần được xem xét:,,,,
Phiên bản:,,,,
Lần xem xét:,,,,
Người xem xét:,,,,
Ngày xem xét:,,,,
Độ lớn sản phẩm được xem xét:,,,,
Nhân công dành cho xem xét (MH):,,,,
,,,,
Câu hỏi,Có,Không,N/A,Ghi chú
Tổng quan,,,,
Code có được viết tốt không (về mặt hình thức) ? Đọc có dễ hiểu không?,,,,
<PERSON><PERSON><PERSON> ghi chú và chuẩn lập trình,,,,
Code có thực hiện theo đúng chuẩn lập trình không?,,,,
Tên biến,,,,
<PERSON>ă<PERSON> lề,,,,
Kiểu dấu ngoặc,,,,
Các file source có đầy đủ tiêu đề đầu trang và thông tin bản quyền không?,,,,
<PERSON><PERSON><PERSON> khai báo biến có được ghi chú đúng cách không?,,,,
Các thành phần dữ liệu dạng số có được khai báo rõ ràng không?,,,,
"Toàn bộ các functions, methods và classes có được ghi chú lại không",,,,
Các đoạn thuật toán phức tap và các đoạn tối ưu code có được ghi chú đầy đủ không?,,,,
Các đoạn code được đánh dấu hủy bỏ có được giải thích không?,,,,
Các ghi chú có được cập nhật kịp thời không?,,,,
Các ghi chú có rõ ràng và đúng không?,,,,
Các ghi chú có tập trung vào giải thích tại sao chứ không phải như thế nào không?,,,,
Tên của mỗi thủ tục/hàm/class có nói lên thủ tục/hàm/class đó làm gì hay không?,,,,
Xử lý lỗi,,,,
Có xử lý lỗi mỗi khi function return không?,,,,
Các trường hợp ngoại lệ có được xử lý không?,,,,
Các hàm gọi đến có được thông báo khi có lỗi không?,,,,
Quản lý tài nguyên,,,,
Các bộ nhớ được cấp phát có được giải phóng không?,,,,
"Toàn bộ các đối tượng (Database connections, Sockets, Files, etc.) có được giải phóng ngay cả trong trường hợp có lỗi không?",,,,
Có đối tượng nào được giải phóng > 1 lần không?,,,,
Thread Safeness,,,,
Việc thay đổi giá trị của các biến dùng trong nhiều thread có được đồng bộ không?,,,,
Các đối tượng dùng trong nhiều thread có được đồng bộ không?,,,,
Có giải phóng các lock theo đúng thứ tự không?,,,,
Có khả năng xảy ra deadlock hoặc tranh chấp lock không?,,,,
Các cấu trúc điều khiển,,,,
Các điều kiện kết thúc vòng lặp có chính xác không?,,,,
Code có bị lặp vô hạn không định trước không?,,,,
Các vòng lặp lồng nhau có được giảm đến mức ít nhất?,,,,
Performance,,,,
Có xảy ra trường hợp trùng lặp cả một object trong khi có thể tham chiếu đến nó không?,,,,
"Code có ảnh hưởng đến kích thước, tốc độ hoặc bộ nhớ sử dụng không?",,,,
Code đã được viết tối ưu chưa?,,,,
Các hàm,,,,
Các biến của hàm có được xác minh rõ ràng trong code không?,,,,
Các mảng có được kiểm tra trường hợp ngoài khoảng không?,,,,
Các biến có được khai báo trước khi sử dụng không?,,,,
Thủ tục/hàm/class có dài quá không? có thể làm đơn giản bằng cách tách ra thành những thủ tục/hàm/class nhỏ hơn không?,,,,
< Thêm tiếp nếu cần>,,,,
,,,,
,,,,
* Nhận xét,,,,
,,,,
,,,,
* Đề xuất,,,,
,,,,
[       ] - Đạt,,,,
[       ] - Xem xét lại,,,,
[       ] - Khác,,,,
