Checklist kiểm tra trước bàn giao,,,,
,,,,
<PERSON><PERSON> dự án,,,,
<PERSON><PERSON><PERSON> đ<PERSON>h giá,,,,
Sản phẩm,,,,
Người đánh giá ,,,,
<PERSON>uồn lực dành để đánh giá (MH) ,,,,
,,,,
<PERSON>â<PERSON> hỏi,<PERSON><PERSON> ,Không,N/A,Ghi chú
Final Inspection cho tài liệu,,,,
<PERSON><PERSON><PERSON> soát tài liệu,,,,
Tài liệu có dấu hiệu bảo mật không?,,,,
Kiểm tra trang ký: xem reviewers and approver c<PERSON> đúng với <PERSON>-RADIO không?,,,,
"Lịch bàn giao hiện thời có tương ứng với lịch bàn giao trong PP không (trường hợp bàn giao hiện thời là phát sinh, nếu không cập nhật lại <PERSON>, th<PERSON> phải kiểm tra trong FI\Project Plan ) ",,,,
<PERSON>ểm tra xem xét,,,,
<PERSON><PERSON> bằng chứng xem xét không?,,,,
<PERSON>ó lỗi không?,,,,
"Nếu có, có nhập đủ vào TestTrack chưa? ",,,,
"Nếu có lỗi trong TestTrack, các thông tin về lỗi có đầy đủ không?",,,,
Các lỗi đã được đóng hết chưa?,,,,
Kiểm tra thay đổi yêu cầu,,,,
Có ghi nhận thay đổi yêu cầu từ khách hàng không? ,,,,
Có ghi vào URD không?,,,,
Có phân tích các thay đổi yêu cầu này không?,,,,
Phân tích yêu cầu cho các thay đổi có được xem xét không?,,,,
"Các tài liệu liên quan (SRS, DD, TC) có cập nhật lại theo các thay đổi không? Trong FI, các thay đổi yêu cầu có được map với SRS, DD, TC, Code chưa?",,,,
Xem xét Final Inspection,,,,
Các lỗi do khách hàng phát hiện đã được đóng hết?,,,,
"Nếu chưa, có liệt kê các lỗi mở trong BB bàn giao không?",,,,
Các pending của lần final inspection trước đã được đóng chưa?,,,,
"Trong thư mục Control các CI đã được cập nhật đúng chưa (VD: DD v1.0, sau khi update thành DD v1.1. Lúc này trong thư mục Control phải lưu cả DDv1.0 và DD v1.1) ",,,,
Có biên bản bàn giao tài liệu không? ,,,,
"Nếu có, phiên bản có khớp với phiên bản của TL bàn giao không?",,,,
PdQA có xem xét tài liệu trước khi final inspection chưa? ,,,,
Final Inspection cho gói phần mềm,,,,
Các thủ tục bàn giao,,,,
Trong Biên bản bàn giao có mô tả:,,,,
Chương trình nguồn/bản chạy,,,,
Phiên bản của chương trình nguồn/bản chạy,,,,
"Các giả thiết, phụ thuộc và ràng buộc",,,,
Lỗi còn tồn tại,,,,
Kế hoạch hành động để sửa lỗi,,,,
Có tài liệu hướng dẫn cài đặt không?,,,,
Thủ tục trước khi cài đặt,,,,
Thủ tục cài đặt,,,,
Thủ tục sau khi cài đặt,,,,
Có lưu gói phần mềm vào thư mục kiểm soát không?,,,,
Check xem những mục trong biên bản bàn giao và trên thực tế có khớp nhau không?,,,,
PdQA có xem xét gói phần mềm trước khi final inspection chưa? ,,,,
Có bàn giao sản phẩm nào do khách hàng cung cấp không? ,,,,
"Nếu có, đã được xem xét chưa?",,,,
Các Comments khi xem xét đã đóng chưa?,,,,
Kiểm tra hoạt động xem xét,,,,
Có xem xét tài liệu thiết kế chi tiết không?,,,,
Có thực hiện xem xét Code không?,,,,
Có thực hiện xem xét kế hoạch test không?,,,,
Có thực hiện xem xét TC không?,,,,
Kiểm tra hoạt động test,,,,
Có nhập lỗi unit test vào TestTrack không?,,,,
Có nhập lỗi system test vào TestTrack không?,,,,
Đã đóng hết lỗi chưa?,,,,
Các lỗi do khách hàng cung cấp đã sửa chưa?,,,,
Đã đóng hết lỗi chưa?,,,,
Nếu chưa thì có liệt kê các lỗi mở trong biên bản bàn giao/thông báo phát hành không?,,,,
Những hành động trễ lại từ lần Final inspection trước có được đóng trong lần bàn giao này không?,,,,
Các tình huống test thử trong khi thực hiện Final inspection có đúng với kết quả mong đợi mà TC đề ra hay không?,,,,
,,0,,0
,,,,
,,,,
,,,,
,,,,
* Nhận xét,,,,
,,,,
* Đề xuất,,,,
,,,,
[X] - Đạt,,,,
[   ] - Xem xét lại,,,,
[   ] - Khác,,,,
