URD Review Checklist,,,,,
,,,,,
Mã dự án,,,,,
Phiên bản sản phẩm,,,,,
<PERSON>ân hệ / Module,,,,,
Lần xem xét,,,,,
Người xem xét,,,,,
<PERSON><PERSON>y xem xét,,,,,
Độ lớn sản phẩm,,,,,
Nguồn lực dành cho xem xét (MH),,,,,
"Hướng dẫn: Trong quá trình review nếu câu trả lời là ""có"" nhưng chưa thực sự đầy đủ thì đánh dấu vào cột ""Có"" và chú thích cụ thể vào cột ""Ghi chú""",,,,,
,,,,,
Câu hỏi,Có,Không,N/A,<PERSON>hi chú,Điều kiện
Kiểm soát tài liệu,,,,,
<PERSON><PERSON><PERSON> liệu có tuân thủ theo yêu cầu kiểm soát tài liệu không:,,,,,B<PERSON>t buộc
Mẫu tài liệu có phải là mẫu mới nhất không?,,,,,Bắt buộc
"Trang tiêu đề có bao gồm đủ tên tài liệu, phiên bản, tên công ty và logo, mã dự án, mã hiệu tài liệu và thời gian ban hành không?",,,,,Bắt buộc
"Đầu và cuối trang có bao gồm tên, phiên bản của tài liệu, Mã hiệu của template không?",,,,,Bắt buộc
Đánh số trang có theo đúng qui định: số thứ tự/tổng số trang?,,,,,Bắt buộc
Trang ký có bao gồm đủ Người xem xét & Người phê duyệt không?,,,,,Bắt buộc
Có bảng theo dõi lịch sử thay đổi không? ,,,,,Bắt buộc
Đối với trường hợp phiên bản tài liệu > v1.0:,,,,,Bắt buộc
Các nội dung ghi nhận trong Bảng ghi nhận thay đổi này có đầy đủ và đúng không?,,,,,Bắt buộc
Các mô tả thay đổi có được ghi nhận phù hợp không?,,,,,Bắt buộc
Tổng quan tài liệu,,,,,
Phần mục đích có bao gồm:,,,,,
Mục tiêu của tài liệu?,,,,,Bắt buộc
Đối tượng sử dụng tài liệu?,,,,,Bắt buộc
Phần phạm vi có bao gồm đầy đủ các yêu cầu:,,,,,
Liệt kê đầy đủ các chức năng mà sản phẩm phần mềm sẽ xây dựng?,,,,,Bắt buộc
Xác định phạm vi các phân hệ / chức năng thuộc sản phẩm phần mềm dự kiến sẽ đáp ứng?,,,,,Bắt buộc
Mô tả kết quả và mục tiêu dự kiến đạt được của mỗi chức năng / phân hệ thuộc phần mềm?,,,,,"Bắt buộc
"
Mô tả điều kiện nghiệm thu và các phương án xử lý mâu thuẫn (nếu có)?,,,,,"Bắt buộc
"
Có bảng thuật ngữ và khái niệm không?,,,,,Bắt buộc
Có liệt kê các tài liệu tham khảo không? ,,,,,
"Nếu có, mỗi tài liệu có bao gồm các thông tin sau không?",,,,,
Tên tài liệu,,,,,
Ngày phát hành,,,,,
Nguồn,,,,,
Có mô tả giới hạn của tài liệu không?,,,,,Bắt buộc
Có mô tả tổ chức của tài liệu không?,,,,,Bắt buộc
Tổng quan hệ thống,,,,,
Phát biểu bài toán,,,,,
"Có rõ ràng, dễ hiểu không?",,,,,Bắt buộc
Có phù hợp với hợp đồng không?,,,,,
Có mô tả mục tiêu hệ thống không?,,,,,Bắt buộc
Có mô tả phạm vi hệ thống không?,,,,,
Đối tượng sử dụng hệ thống,,,,,Bắt buộc
Mô hình tổng thể hệ thống,,,,,Bắt buộc
Chung cho toàn bộ các yêu cầu,,,,,
Các yêu cầu chung có được cụ thể hóa để có thể dễ dàng kiểm tra được không?,,,,,Bắt buộc
Mỗi yêu cầu có được xác định duy nhất và chính xác không?,,,,,Bắt buộc
Các yêu cầu có thống nhất không?,,,,,Bắt buộc
Có thứ tự ưu tiên / độ ưu tiên thực hiện cho các yêu cầu không?,,,,,Bắt buộc
Có ghi nhận mã hiệu và tên gọi cho tất cả các yêu cầu không?,,,,,Bắt buộc
Các dẫn chiếu đến các yêu cầu khác trong tài liệu có đúng không?,,,,,
Có mô tả tiêu chí nghiệm thu của các yêu cầu trong tài liệu không?,,,,,Bắt buộc
Các yêu cầu có đầy đủ thông tin cần thiết không?,,,,,
Nếu có yêu cầu nào thiếu có được đánh dấu lại không?,,,,,
"Các yêu cầu khác về chất lượng có được mô tả rõ ràng và được số hoá, với những điều chỉnh có thể chấp nhận được không?",,,,,
Các yêu cầu có nằm trong phạm vi dự án không?,,,,,
Các yêu cầu có thực sự là yêu cầu mà không phải là đề xuất thiết kế hoặc giải pháp thực hiện không?,,,,,
Hành động cần làm trong những trường hợp có lỗi (nếu có) có được mô tả không?,,,,,
Qui trình nghiệp vụ,,,,,
Có mô tả các qui trình nghiệp vụ không?,,,,,
Với mỗi qui trình nghiệp vụ có mô tả:,,,,,
Sự kiện kích hoạt qui trình?,,,,,
Mô hình qui trình nghiệp vụ?,,,,,
Các bước trong qui trình?,,,,,Bắt buộc
Các yêu cầu chức năng,,,,,
Mỗi yêu cầu có bao gồm đủ các thông tin sau không?,,,,,
Mã hiệu yêu cầu – tên yêu cầu,,,,,Bắt buộc
Mô tả chức năng,,,,,Bắt buộc
Nguồn cung cấp yêu cầu,,,,,
Tác nhân thực hiện,,,,,
Điều kiện để thực hiện,,,,,
"Các bước xử lý chính (tác nghiệp của đối tượng, luồng thông tin, kết xuất, ...)",,,,,Bắt buộc
Các bước xử lý ngoại lệ,,,,,Bắt buộc
Điều kiện kết thúc,,,,,
Các yêu cầu khác,,,,,
Các yêu cầu về bảo mật và an toàn có được xác định không?,,,,,Bắt buộc
Loại bảo mật nào được yêu cầu?,,,,,
Có xác định các yêu cầu về sao lưu dữ liệu không?,,,,,
Có xác định các yêu cầu về tính sử dụng (usability) của hệ thống không?,,,,,
Có xác định các yêu cầu về tính ổn định (stability) của hệ thống không?,,,,,
Các yêu cầu về performance (hiệu năng) có được xác định không?,,,,,
Lưu lượng giao dịch và qui mô của dữ liệu có được chỉ rõ trong tài liệu không?,,,,,Bắt buộc
Có xác định các yêu cầu về tính hỗ trợ của hệ thống không?,,,,,
Có mô tả các yêu cầu về công nghệ và các ràng buộc đối với thiết kế không?,,,,,Bắt buộc
Có xác định các yêu cầu giao tiếp của hệ thống không?,,,,,
Giao tiếp người dùng (GUI)?,,,,,Bắt buộc
Giao tiếp phần cứng?,,,,,
Giao tiếp phần mềm bao gồm:,,,,,
Giao tiếp với các hệ thống phần mềm khác?,,,,,Bắt buộc
Giao tiếp giữa các module trong nội tại hệ thống?,,,,,Bắt buộc
Giao tiếp truyền thông?,,,,,
Có xác định các yêu cầu về tài liệu người dùng và hỗ trợ trực tuyến không?,,,,,
Hệ thống có sử dụng các thành phần mua ngoài không?,,,,,
"Nếu có, có được mô tả đầy đủ trong tài liệu không?",,,,,Bắt buộc
"Các yêu cầu pháp lý, bản quyền không?",,,,,
"Nếu có, có được mô tả đầy đủ trong tài liệu không?",,,,,Bắt buộc
Hệ thống có áp dụng các tiêu chuẩn nào khác ngoài các tiêu chuẩn thuộc phạm vi dự án không?,,,,,
"Nếu có, có được mô tả đầy đủ trong tài liệu không?",,,,,Bắt buộc
,,,,,
<Có thể thêm vào checklist các quan tâm khác nếu cần thiết>,,,,,
,,0,,0,
* Ý kiến,,,,,
,,,,,
,,,,,
* Đề xuất,,,,,
,,,,,
[X] - Đạt,,,,,
[  ] - Xem xét lại,,,,,
[  ] - Khác,,,,,
