{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ptit-front": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["ng2-cookies"], "outputPath": "dist/ptit-front", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.json", "src/firebase-messaging-sw.js"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/bootstrap/dist/css/bootstrap.css", "./node_modules/ngx-toastr/toastr.css", "./node_modules/primeicons/primeicons.css", "./node_modules/primeng/resources/themes/lara-light-blue/theme.css", "./node_modules/primeng/resources/primeng.min.css", "src/styles.css"], "scripts": ["src/assets/libs/bootstrap/js/bootstrap.bundle.min.js", "src/assets/libs/simplebar/simplebar.min.js", "src/assets/libs/node-waves/waves.min.js", "src/assets/libs/feather-icons/feather.min.js", "src/assets/js/pages/plugins/lord-icon-2.1.0.js", "src/assets/js/plugins.js", "src/assets/libs/prismjs/prism.js", "src/assets/js/custom.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "ptit-front:build:production"}, "development": {"browserTarget": "ptit-front:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ptit-front:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}