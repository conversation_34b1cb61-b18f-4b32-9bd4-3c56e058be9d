var autoScroll=function(){"use strict";function e(e,n){return void 0===e?void 0===n?e:n:e}function n(n,t){return n=e(n,t),"function"==typeof n?function(){for(var e=arguments,t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=e[o];return!!n.apply(this,r)}:n?function(){return!0}:function(){return!1}}function t(e,n){if(n=u(n,!0),!x(n))return-1;for(var t=0;t<e.length;t++)if(e[t]===n)return t;return-1}function r(e,n){return-1!==t(e,n)}function o(e,n){for(var t=0;t<n.length;t++)r(e,n[t])||e.push(n[t]);return n}function i(e){for(var n=arguments,t=[],r=arguments.length-1;r-- >0;)t[r]=n[r+1];return t=t.map(u),o(e,t)}function a(e){for(var n=arguments,r=[],o=arguments.length-1;o-- >0;)r[o]=n[o+1];return r.map(u).reduce(function(n,r){var o=t(e,r);return-1!==o?n.concat(e.splice(o,1)):n},[])}function u(e,n){if("string"==typeof e)try{return document.querySelector(e)}catch(e){throw e}if(!x(e)&&!n)throw new TypeError(e+" is not a DOM element.");return e}function c(e,t){t=t||{};var r=n(t.allowUpdate,!0);return function(n){if(n=n||window.event,e.target=n.target||n.srcElement||n.originalTarget,e.element=this,e.type=n.type,r(n)){if(n.targetTouches)e.x=n.targetTouches[0].clientX,e.y=n.targetTouches[0].clientY,e.pageX=n.targetTouches[0].pageX,e.pageY=n.targetTouches[0].pageY,e.screenX=n.targetTouches[0].screenX,e.screenY=n.targetTouches[0].screenY;else{if(null===n.pageX&&null!==n.clientX){var t=n.target&&n.target.ownerDocument||document,o=t.documentElement,i=t.body;e.pageX=n.clientX+(o&&o.scrollLeft||i&&i.scrollLeft||0)-(o&&o.clientLeft||i&&i.clientLeft||0),e.pageY=n.clientY+(o&&o.scrollTop||i&&i.scrollTop||0)-(o&&o.clientTop||i&&i.clientTop||0)}else e.pageX=n.pageX,e.pageY=n.pageY;e.x=n.clientX,e.y=n.clientY,e.screenX=n.screenX,e.screenY=n.screenY}e.clientX=e.x,e.clientY=e.y}}}function l(){var e={top:{value:0,enumerable:!0},left:{value:0,enumerable:!0},right:{value:window.innerWidth,enumerable:!0},bottom:{value:window.innerHeight,enumerable:!0},width:{value:window.innerWidth,enumerable:!0},height:{value:window.innerHeight,enumerable:!0},x:{value:0,enumerable:!0},y:{value:0,enumerable:!0}};if(Object.create)return Object.create({},e);var n={};return Object.defineProperties(n,e),n}function f(e){if(e===window)return l();try{var n=e.getBoundingClientRect();return void 0===n.x&&(n.x=n.left,n.y=n.top),n}catch(n){throw new TypeError("Can't call getBoundingClientRect on "+e)}}function d(e,n){var t=f(n);return e.y>t.top&&e.y<t.bottom&&e.x>t.left&&e.x<t.right}function s(e){function n(e){for(var n=0;n<Y.length;n++)r[Y[n]]=e[Y[n]]}function t(){e&&e.removeEventListener("mousemove",n,!1),r=null}var r={screenX:0,screenY:0,clientX:0,clientY:0,ctrlKey:!1,shiftKey:!1,altKey:!1,metaKey:!1,button:0,buttons:1,relatedTarget:null,region:null};return void 0!==e&&e.addEventListener("mousemove",n),{destroy:t,dispatch:function(){return MouseEvent?function(e,n,t){var o=new MouseEvent("mousemove",m(r,n));return v(o,t),e.dispatchEvent(o)}:"function"==typeof document.createEvent?function(e,n,t){var o=m(r,n),i=document.createEvent("MouseEvents");return i.initMouseEvent("mousemove",!0,!0,window,0,o.screenX,o.screenY,o.clientX,o.clientY,o.ctrlKey,o.altKey,o.shiftKey,o.metaKey,o.button,o.relatedTarget),v(i,t),e.dispatchEvent(i)}:"function"==typeof document.createEventObject?function(e,n,t){var o=document.createEventObject(),i=m(r,n);for(var a in i)o[a]=i[a];return v(o,t),e.dispatchEvent(o)}:void 0}()}}function m(e,n){n=n||{};for(var t=X(e),r=0;r<Y.length;r++)void 0!==n[Y[r]]&&(t[Y[r]]=n[Y[r]]);return t}function v(e,n){console.log("data ",n),e.data=n||{},e.dispatched="mousemove"}function w(e,t){function o(n){for(var t=0;t<e.length;t++)if(e[t]===n.target){A=!0;break}A&&y(function(){return A=!1})}function u(){M=!0}function l(){M=!1,d()}function d(){b(q),b(F)}function m(){M=!1}function v(n){if(!n)return null;if(N===n)return n;if(r(e,n))return n;for(;n=n.parentNode;)if(r(e,n))return n;return null}function w(){for(var n=null,t=0;t<e.length;t++)h(O,e[t])&&(n=e[t]);return n}function p(e){if(X.autoScroll()&&!e.dispatched){var n=e.target,t=document.body;N&&!h(O,N)&&(X.scrollWhenOutside||(N=null)),n&&n.parentNode===t?n=w():(n=v(n))||(n=w()),n&&n!==N&&(N=n),K&&(b(F),F=y(g)),N&&(b(q),q=y(E))}}function g(){T(K),b(F),F=y(g)}function E(){N&&(T(N),b(q),q=y(E))}function T(e){var n,t,r=f(e);n=O.x<r.left+X.margin?Math.floor(Math.max(-1,(O.x-r.left)/X.margin-1)*X.maxSpeed):O.x>r.right-X.margin?Math.ceil(Math.min(1,(O.x-r.right)/X.margin+1)*X.maxSpeed):0,t=O.y<r.top+X.margin?Math.floor(Math.max(-1,(O.y-r.top)/X.margin-1)*X.maxSpeed):O.y>r.bottom-X.margin?Math.ceil(Math.min(1,(O.y-r.bottom)/X.margin+1)*X.maxSpeed):0,X.syncMove()&&j.dispatch(e,{pageX:O.pageX+n,pageY:O.pageY+t,clientX:O.x+n,clientY:O.y+t}),setTimeout(function(){t&&x(e,t),n&&L(e,n)})}function x(e,n){e===window?window.scrollTo(e.pageXOffset,e.pageYOffset+n):e.scrollTop+=n}function L(e,n){e===window?window.scrollTo(e.pageXOffset+n,e.pageYOffset):e.scrollLeft+=n}void 0===t&&(t={});var X=this,Y=4,A=!1;this.margin=t.margin||-1,this.scrollWhenOutside=t.scrollWhenOutside||!1;var O={},S=c(O),j=s(),M=!1;window.addEventListener("mousemove",S,!1),window.addEventListener("touchmove",S,!1),isNaN(t.maxSpeed)||(Y=t.maxSpeed),this.autoScroll=n(t.autoScroll),this.syncMove=n(t.syncMove,!1),this.destroy=function(n){window.removeEventListener("mousemove",S,!1),window.removeEventListener("touchmove",S,!1),window.removeEventListener("mousedown",u,!1),window.removeEventListener("touchstart",u,!1),window.removeEventListener("mouseup",l,!1),window.removeEventListener("touchend",l,!1),window.removeEventListener("pointerup",l,!1),window.removeEventListener("mouseleave",m,!1),window.removeEventListener("mousemove",p,!1),window.removeEventListener("touchmove",p,!1),window.removeEventListener("scroll",o,!0),e=[],n&&d()},this.add=function(){for(var n=[],t=arguments.length;t--;)n[t]=arguments[t];return i.apply(void 0,[e].concat(n)),this},this.remove=function(){for(var n=[],t=arguments.length;t--;)n[t]=arguments[t];return a.apply(void 0,[e].concat(n))};var F,K=null;"[object Array]"!==Object.prototype.toString.call(e)&&(e=[e]),function(n){e=[],n.forEach(function(e){e===window?K=window:X.add(e)})}(e),Object.defineProperties(this,{down:{get:function(){return M}},maxSpeed:{get:function(){return Y}},point:{get:function(){return O}},scrolling:{get:function(){return A}}});var q,N=null;window.addEventListener("mousedown",u,!1),window.addEventListener("touchstart",u,!1),window.addEventListener("mouseup",l,!1),window.addEventListener("touchend",l,!1),window.addEventListener("pointerup",l,!1),window.addEventListener("mousemove",p,!1),window.addEventListener("touchmove",p,!1),window.addEventListener("mouseleave",m,!1),window.addEventListener("scroll",o,!0)}function p(e,n){return new w(e,n)}function h(e,n,t){return t?e.y>t.top&&e.y<t.bottom&&e.x>t.left&&e.x<t.right:d(e,n)}var g=["webkit","moz","ms","o"],y=function(){for(var e=0,n=g.length;e<n&&!window.requestAnimationFrame;++e)window.requestAnimationFrame=window[g[e]+"RequestAnimationFrame"];return window.requestAnimationFrame||function(){var e=0;window.requestAnimationFrame=function(n){var t=(new Date).getTime(),r=Math.max(0,16-t-e),o=window.setTimeout(function(){return n(t+r)},r);return e=t+r,o}}(),window.requestAnimationFrame.bind(window)}(),b=function(){for(var e=0,n=g.length;e<n&&!window.cancelAnimationFrame;++e)window.cancelAnimationFrame=window[g[e]+"CancelAnimationFrame"]||window[g[e]+"CancelRequestAnimationFrame"];return window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){window.clearTimeout(e)}),window.cancelAnimationFrame.bind(window)}(),E=function(){var e=function(e){return"function"==typeof e},n=function(e){var n=Number(e);return isNaN(n)?0:0!==n&&isFinite(n)?(n>0?1:-1)*Math.floor(Math.abs(n)):n},t=Math.pow(2,53)-1,r=function(e){var r=n(e);return Math.min(Math.max(r,0),t)},o=function(e){if(null!=e){if(["string","number","boolean","symbol"].indexOf(typeof e)>-1)return Symbol.iterator;if("undefined"!=typeof Symbol&&"iterator"in Symbol&&Symbol.iterator in e)return Symbol.iterator;if("@@iterator"in e)return"@@iterator"}},i=function(n,t){if(null!=n&&null!=t){var r=n[t];if(null==r)return;if(!e(r))throw new TypeError(r+" is not a function");return r}},a=function(e){var n=e.next();return!Boolean(n.done)&&n};return function(n){var t,u=this,c=arguments.length>1?arguments[1]:void 0;if(void 0!==c){if(!e(c))throw new TypeError("Array.from: when provided, the second argument must be a function");arguments.length>2&&(t=arguments[2])}var l,f,d=i(n,o(n));if(void 0!==d){l=e(u)?Object(new u):[];var s=d.call(n);if(null==s)throw new TypeError("Array.from requires an array-like or iterable object");f=0;for(var m,v;;){if(!(m=a(s)))return l.length=f,l;v=m.value,l[f]=c?c.call(t,v,f):v,f++}}else{var w=Object(n);if(null==n)throw new TypeError("Array.from requires an array-like object - not null or undefined");var p=r(w.length);l=e(u)?Object(new u(p)):new Array(p),f=0;for(var h;f<p;)h=w[f],l[f]=c?c.call(t,h,f):h,f++;l.length=p}return l}}(),T=("function"==typeof Array.from&&Array.from,Array.isArray,Object.prototype.toString,"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}),x=function(e){return null!=e&&"object"===(void 0===e?"undefined":T(e))&&1===e.nodeType&&"object"===T(e.style)&&"object"===T(e.ownerDocument)},L=void 0;L="function"!=typeof Object.create?function(e){var n=function(){};return function(e,t){if(e!==Object(e)&&null!==e)throw TypeError("Argument must be an object, or null");n.prototype=e||{};var r=new n;return n.prototype=null,void 0!==t&&Object.defineProperties(r,t),null===e&&(r.__proto__=null),r}}():Object.create;var X=L,Y=["altKey","button","buttons","clientX","clientY","ctrlKey","metaKey","movementX","movementY","offsetX","offsetY","pageX","pageY","region","relatedTarget","screenX","screenY","shiftKey","which","x","y"];return p}();