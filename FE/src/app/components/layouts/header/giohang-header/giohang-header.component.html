<!-- giohang -->
<div
  class="dropdown topbar-head-dropdown ms-1 header-item hover-dropdown"
  id="hover-dropdown"
  [class.hover-active]="isHoverDropdown && !isFirst"
>
  <button
    type="button"
    class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none"
    id="page-headerr-cartt-dropdown"
    data-bs-toggle="dropdown"
    data-bs-auto-close="outside"
    aria-haspopup="true"
    aria-expanded="false"
    [routerLink]="['/user/giohang']"
  >
    <i class="bx bx-shopping-bag fs-22"></i>
    <span
      *ngIf="gioHangLst"
      class="position-absolute topbar-badge fs-10 translate-middle badge rounded-pill bg-info"
      >{{ gioHangLst.length }}</span
    >
  </button>

  <div
    class="dropdown-menu dropdown-menu-xl dropdown-menu-end p-0 dropdown-menu-cart"
    aria-labelledby="page-header-cart-dropdown"
  >
    <div
      class="p-3 border-top-0 border-start-0 border-end-0 border-dashed border"
    >
      <div class="row align-items-center">
        <div class="col">
          <h6 class="m-0 fs-16 fw-semibold">Giỏ hàng</h6>
        </div>
        <div class="col-auto">
          <span class="badge badge-soft-warning fs-13"
            ><span class="" *ngIf="gioHangLst">{{ gioHangLst.length }}</span>
            sản phẩm</span
          >
        </div>
      </div>
    </div>
    <div style="max-height: 300px">
      <div class="p-2">
        <div
          class="text-center empty-cart"
          *ngIf="gioHangLst && gioHangLst.length == 0"
        >
          <div class="avatar-md mx-auto my-3">
            <div
              class="avatar-title bg-soft-info text-info fs-36 rounded-circle"
            >
              <i class="bx bx-cart"></i>
            </div>
          </div>
          <h5 class="mb-3">Giỏ hàng của bạn trống!</h5>
          <a [routerLink]="['/user/home']" class="btn btn-success w-md mb-3"
            >Mua ngay bây giờ</a
          >
        </div>
        <div
          *ngFor="let item of gioHangLst"
          class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2"
        >
          <div class="d-flex align-items-center">
            <img
              [src]="item.thuoc?.avatar"
              class="me-3 rounded-circle avatar-sm p-2 bg-light"
              alt="user-pic"
            />
            <div class="flex-1">
              <h6 class="mt-0 mb-1 fs-14">
                <a
                  [routerLink]="['/thuoc-chitiet/', item.thuoc?.id]"
                  class="text-reset"
                  >{{ item.thuoc?.tenThuoc }}</a
                >
              </h6>
              <p class="mb-0 fs-12 text-muted">
                Số lượng:
                <span>{{ item.soLuong }} {{ item.thuoc?.donVi }}</span>
              </p>
            </div>
            <div class="px-2">
              <h5 class="m-0 fw-normal">
                <span class="cart-item-price text-info fw-semibold"
                  >{{ item.thuoc?.giaBan | number : "1.0-0" }} VNĐ</span
                >
              </h5>
            </div>
            <div class="ps-2">
              <button
                type="button"
                class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"
                (click)="deleteGioHang(item)"
              >
                <i class="ri-close-fill fs-16"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      *ngIf="gioHangLst && gioHangLst.length > 0"
      class="p-3 border-bottom-0 border-start-0 border-end-0 border-dashed border"
      id="checkout-elem"
    >
      <a
        [routerLink]="['/user/giohang']"
        class="btn btn-success text-center w-100"
      >
        Xem chi tiết
      </a>
    </div>
  </div>
</div>
