<header id="page-topbar">
  <div class="layout-width">
    <div class="navbar-header">
      <div class="d-flex">
        <!-- LOGO -->
        <div class="navbar-brand-box horizontal-logo">
          <a href="index.html" class="logo logo-dark d-flex align-items-center">
            <span class="logo-sm">
              <img src="assets/images/logo.png" alt="" height="40" />
            </span>
            <span class="logo-lg">
              <img src="assets/images/logo.png" alt="" height="40" />
            </span>
          </a>

          <a href="index.html" class="logo logo-light">
            <span class="logo-sm">
              <img src="assets/images/ptit.png" alt="" height="40" />
            </span>
            <span class="logo-lg">
              <img src="assets/images/ptit.png" alt="" height="40" />
            </span>
          </a>
        </div>

        <button
          type="button"
          class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger shadow-none"
          id="topnav-hamburger-icon"
        >
          <span class="hamburger-icon">
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>

      <div class="d-flex align-items-center">
        <!-- <div class="dropdown topbar-head-dropdown header-item">
          <input
          type="text"
          class="form-control search"
          placeholder="Tìm kiếm..."
        />

          <button
            type="button"
            class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none"
            id="page-header-search-dropdown"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <i class="bx bx-search fs-22"></i>
          </button>
        </div> -->

        <div class="ms-1 header-item d-none d-sm-flex">
          <button
            type="button"
            class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none"
            data-toggle="fullscreen"
          >
            <i class="bx bx-fullscreen fs-22"></i>
          </button>
        </div>

        <app-giohang-header
          *ngIf="isAuthenticate && !isAdmin"
        ></app-giohang-header>

        <!-- thông báo -->
        <app-thongbao-header
          *ngIf="isAuthenticate && !isAdmin"
        ></app-thongbao-header>

        <div *ngIf="!isAuthenticate" class="d-flex ms-2" style="margin: 0px">
          <button type="button" class="btn btn-danger btn-sm" (click)="login()">
            Đăng nhập
          </button>
        </div>

        <div *ngIf="isAuthenticate" class="header-item ms-1">
          <button
            type="button"
            class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none"
            id="user-dropdown"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <i class="bx bx-user fs-22"></i>
          </button>
          <div class="dropdown-menu dropdown-menu-end" style="cursor: pointer">
            <!-- Menu item -->
            <a class="dropdown-item" [routerLink]="['/user/profile']"
              ><i
                class="mdi mdi-account-circle text-muted fs-16 align-middle me-1"
              ></i>
              <span class="align-middle"> Hồ sơ </span>
            </a>

            <a
              *ngIf="!isAdmin"
              class="dropdown-item"
              [routerLink]="['/user/donmua']"
            >
              <i class="ri-store-2-fill me-1 align-middle fs-4"></i>

              <span class="align-middle"> Đơn mua </span>
            </a>

            <a class="dropdown-item" (click)="logout()"
              ><i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i>
              <span class="align-middle" data-key="t-logout">Đăng xuất</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
