<div class="app-menu navbar-menu">
  <!-- LOGO -->
  <div class="navbar-brand-box">
    <!-- Light Logo-->
    <a href="index.html" class="logo logo-light">
      <span class="logo-sm">
        <img src="assets/images/logo-sm.png" alt="" height="22" />
      </span>
      <span class="logo-lg">
        <img src="assets/images/logo-light.png" alt="" height="17" />
      </span>
    </a>
    <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
      <i class="ri-record-circle-line"></i>
    </button>
  </div>

  <div id="scrollbar">
    <div class="container-fluid">
      <div id="two-column-menu"></div>
      <ul class="navbar-nav" id="navbar-nav">
        <li class="menu-title"><span data-key="t-menu">Menu</span></li>
        <li class="nav-item">
          <a class="nav-link menu-link" role="button" [routerLink]="['/sys/thongke']">
            <i class="mdi mdi-view-carousel-outline"></i>
            <span data-key="t-layouts">Báo cáo</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link menu-link" data-bs-toggle="collapse" role="button" aria-expanded="false"
            aria-controls="sidebarDashboards">
            <i class="mdi mdi-speedometer"></i>
            <span data-key="t-dashboards"> Quản lý thuốc</span>
          </a>
          <div class="collapse menu-dropdown" id="sidebarDashboards">
            <ul class="nav nav-sm flex-column">
              <li class="nav-item">
                <a [routerLink]="['/sys/product']" class="nav-link" data-key="t-analytics">
                  Thuốc
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/ncc']" class="nav-link" data-key="t-ecommerce">
                  Nhà cung cấp
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/nsx']" class="nav-link" data-key="t-ecommerce">
                  Nhà sản xuất
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/loaithuoc']" class="nav-link" data-key="t-ecommerce">
                  Loại thuốc
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/danhmucThuoc']" class="nav-link" data-key="t-ecommerce">
                  Danh mục thuốc
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/doituong']" class="nav-link" data-key="t-ecommerce">
                  Đối tượng
                </a>
              </li>
            </ul>
          </div>
        </li>

        <li class="nav-item">
          <a class="nav-link menu-link" href="#sidebarLayouts" data-bs-toggle="collapse" role="button"
            aria-expanded="false" aria-controls="sidebarLayouts">
            <i class="mdi mdi-view-carousel-outline"></i>
            <span data-key="t-layouts">Quản lý hàng</span>
          </a>
          <div class="collapse menu-dropdown" id="sidebarLayouts">
            <ul class="nav nav-sm flex-column">
              <li class="nav-item">
                <a [routerLink]="['/sys/phieunhap']" class="nav-link" data-key="t-analytics">
                  Nhập hàng
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/donhang']" class="nav-link" data-key="t-analytics">
                  Đơn hàng
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/tonkho']" class="nav-link" data-key="t-analytics">
                  Tồn kho
                </a>
              </li>
            </ul>
          </div>
        </li>

        <li class="nav-item">
          <a class="nav-link menu-link" [routerLink]="['/sys/donhang-create']">
            <i class="mdi mdi-storefront-outline"></i>
            <span data-key="t-layouts">Tạo đơn hàng</span>
          </a>
          <!-- <div class="collapse menu-dropdown" id="sidebarLayouts">
            <ul class="nav nav-sm flex-column"></ul>
          </div> -->
        </li>

        <li class="nav-item">
          <a class="nav-link menu-link" [routerLink]="['/sys/thongbao']">
            <i class="mdi mdi-bell-alert-outline"></i>
            <span data-key="t-layouts">Thông báo</span>
          </a>
          <!-- <div class="collapse menu-dropdown" id="sidebarLayouts">
            <ul class="nav nav-sm flex-column"></ul>
          </div> -->
        </li>

        <li class="nav-item">
          <a class="nav-link menu-link" href="#sidebarLayouts" data-bs-toggle="collapse" role="button"
            aria-expanded="false" aria-controls="sidebarLayouts">
            <i class="mdi mdi-account-circle-outline"></i>
            <span data-key="t-layouts">Quản lý khách hàng</span>
          </a>
          <div class="collapse menu-dropdown" id="sidebarLayouts">
            <ul class="nav nav-sm flex-column">
              <li class="nav-item">
                <a [routerLink]="['/sys/customer']" class="nav-link" data-key="t-analytics">
                  Khách hàng
                </a>
              </li>

              <li class="nav-item">
                <a [routerLink]="['/sys/chucnang']" class="nav-link" data-key="t-analytics">
                  Chức năng
                </a>
              </li>
            </ul>
          </div>
        </li>

        <!-- <li class="nav-item">
          <a
            class="nav-link menu-link"
            [routerLink]="['/home']"
            role="button"
            aria-expanded="false"
            aria-controls="sidebarLayouts"
          >
            <i class="mdi mdi-view-carousel-outline"></i>
            <span data-key="t-layouts">Trang chủ</span>
          </a>
        </li> -->
        <!-- <li class="nav-item" *ngIf="isAdmin">
          <a class="nav-link" href="#adminPanel">Quản Lí Home</a>
        </li>
      
        <li class="nav-item" *ngIf="isCustomer">
          <a class="nav-link" href="#customerHome">Khách Hàng Home</a>
        </li> -->
      </ul>
    </div>
  </div>

  <div class="sidebar-background"></div>
</div>