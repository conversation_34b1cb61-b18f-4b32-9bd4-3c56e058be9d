<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Giỏ hàng</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <div class="row mb-3">
        <div class="col-xl-8">
          <div class="row align-items-center gy-3 mb-3">
            <div class="col-sm">
              <div>
                <h5 class="fs-14 mb-0">
                  Tổng có: {{ gioHangLst.length }} sản phẩm
                </h5>
              </div>
            </div>
            <div class="col-sm-auto">
              <a [routerLink]="['/user/home']" class="link-primary text-decoration-underline">Thêm thuốc</a>
            </div>
          </div>

          <div *ngFor="let item of gioHangLst" class="card product">
            <div class="card-body">
              <div class="row gy-3">
                <div class="col-sm-auto">
                  <div class="avatar-lg bg-light rounded p-1">
                    <img [src]="item.thuoc?.avatar" alt="" class="img-fluid d-block" />
                  </div>
                </div>

                <div class="col-sm">
                  <h5 class="fs-14 text-truncate">
                    <a [routerLink]="['/thuoc-chitiet/', item.thuoc?.id]" class="text-dark">{{ item.thuoc?.tenThuoc
                      }}</a>
                  </h5>
                  <ul class="list-inline text-muted">
                    <li class="list-inline-item">
                      Số lượng còn lại :
                      <span class="fw-medium">{{
                        item.thuoc?.soLuongTon
                        }}</span>
                    </li>
                    <!-- <li class="list-inline-item">
                      Size : <span class="fw-medium">M</span>
                    </li> -->
                  </ul>

                  <div class="input-step light">
                    <button type="button" class="minus shadow" (click)="minusQuantity(item)">
                      –
                    </button>
                    <input type="number" class="product-quantity" [value]="item.soLuong" min="0" max="100" />
                    <button type="button" class="plus shadow" (click)="addQuantity(item)">
                      +
                    </button>
                  </div>
                </div>

                <div class="col-sm-auto">
                  <div class="text-lg-end">
                    <p class="text-muted mb-1">Giá:</p>
                    <h5 class="fs-14">
                      <span id="ticket_price" class="product-price">{{ item.thuoc?.giaBan | number : "1.0-0" }}
                        VNĐ</span>
                    </h5>
                  </div>
                </div>
              </div>
            </div>
            <!-- card body -->
            <div class="card-footer">
              <div class="row align-items-center gy-3">
                <div class="col-sm">
                  <div class="d-flex flex-wrap my-n1">
                    <div>
                      <a (click)="deleteGioHang(item)" class="d-block text-body p-1 px-2" data-bs-toggle="modal"
                        data-bs-target="#removeItemModal"><i
                          class="ri-delete-bin-fill text-danger align-bottom me-1"></i>
                        Xoá</a>
                    </div>
                    <!-- <div>
                      <a href="#" class="d-block text-body p-1 px-2"
                        ><i
                          class="ri-star-fill text-muted align-bottom me-1"
                        ></i>
                        Add Wishlist</a
                      >
                    </div> -->
                  </div>
                </div>
                <div class="col-sm-auto">
                  <div class="d-flex align-items-center gap-2 text-muted">
                    <div>Tổng :</div>
                    <h5 class="fs-14 mb-0">
                      <span class="product-line-price">
                        {{
                        (item.soLuong || 0) * (item.thuoc?.giaBan || 0)
                        | number : "1.0-0"
                        }}
                        VNĐ
                      </span>
                    </h5>
                  </div>
                </div>
              </div>
            </div>
            <!-- end card footer -->
          </div>
          <!-- end card -->

          <div *ngIf="gioHangLst.length == 0" id="pills-finish">
            <div class="card">
              <div class="card-body">
                <div class="text-center py-5 cursor-pointer" [routerLink]="['/user/home']">
                  <div class="mb-4">
                    <lord-icon src="https://cdn.lordicon.com/lupuorrc.json" trigger="loop"
                      colors="primary:#0ab39c,secondary:#405189" style="width: 120px; height: 120px"></lord-icon>
                  </div>
                  <h5>Hiện tại giỏ hàng của bạn đang trống!</h5>
                  <p class="text-muted">Hãy cùng đi thêm thuốc vào giỏ hàng</p>

                  <!-- <h3 class="fw-semibold">
                    Order ID:
                    <a
                      href="apps-ecommerce-order-details.html"
                      class="text-decoration-underline"
                      >VZ2451</a
                    >
                  </h3> -->
                </div>
              </div>
            </div>
          </div>

          <div class="text-end mb-4">
            <a *ngIf="gioHangLst.length > 0" [routerLink]="['/user/checkout']"
              class="btn btn-success btn-label right ms-auto"><i
                class="ri-arrow-right-line label-icon align-bottom fs-16 ms-2"></i>
              Mua</a>
          </div>
        </div>
        <!-- end col -->

        <div class="col-xl-4">
          <div class="sticky-side-div">
            <div class="card">
              <div class="card-header border-bottom-dashed">
                <h5 class="card-title mb-0">Hóa đơn</h5>
              </div>
              <!-- <div class="card-header bg-soft-light border-bottom-dashed">
                <div class="text-center">
                  <h6 class="mb-2">
                    Have a <span class="fw-semibold">promo</span> code ?
                  </h6>
                </div>
                <div class="hstack gap-3 px-3 mx-n3">
                  <input
                    class="form-control me-auto"
                    type="text"
                    placeholder="Enter coupon code"
                    aria-label="Add Promo Code here..."
                  />
                  <button type="button" class="btn btn-success w-xs">
                    Apply
                  </button>
                </div>
              </div> -->
              <div class="card-body pt-2">
                <div class="table-responsive">
                  <table class="table table-borderless mb-0">
                    <tbody>
                      <tr>
                        <td>Tổng:</td>
                        <td class="text-end" id="cart-subtotal">
                          {{ tongTien | number : "1.0-0"}} VNĐ
                        </td>
                      </tr>
                      <tr>
                        <td>Giảm giá:</td>
                        <td class="text-end" id="cart-discount">- 0</td>
                      </tr>
                      <tr>
                        <td>Phí vận chuyển :</td>
                        <td class="text-end" id="cart-shipping">0</td>
                      </tr>

                      <tr class="table-active">
                        <th>Total (USD) :</th>
                        <td class="text-end">
                          <span class="fw-semibold" id="cart-total">
                            {{ tongTien | number : "1.0-0" }} VNĐ
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- end table-responsive -->
              </div>
            </div>
          </div>
          <!-- end stickey -->
        </div>
      </div>
      <!-- end row -->
    </div>
    <!-- container-fluid -->
  </div>
  <!-- End Page-content -->
</div>