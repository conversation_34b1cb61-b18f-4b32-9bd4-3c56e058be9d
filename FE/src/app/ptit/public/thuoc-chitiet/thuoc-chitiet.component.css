.btn-custom-primary {
  background-color: #3964e7 ; 
  border-color: #3964e7 ;
  color: white ;
}

.btn-custom-primary:hover {
  background-color: #3964e7 ; 
  border-color: #3964e7 ;
}

.rating {
  margin-bottom: 0;
  font-size: 16px;
}

.text-muted {
  font-size: 16px;
}

.text-warning {
  color: #ffc107; /* Màu vàng cho sao */
}

h3{
  color: #000;
  font-size: 25px;
}
.gap-2 {
  gap: 8px; /* Khoảng cách giữa các phần tử */
}

.price .current-price {
  font-size: 30px;
  font-weight: bold;
  color: #007bff;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 10px; /* K<PERSON>ảng cách giữa các mục */
}

.product-details li {
  display: grid;
  grid-template-columns: 150px 1fr; /* Cột 1 cố định 150px, cột 2 tự giãn */
  gap: 10px; /* K<PERSON>ảng cách giữa cột nhãn và giá trị */
  align-items: center; /* <PERSON>h giữa theo chiều dọc */
  padding: 8px 0;
  border-bottom: 1px solid #eaeaea; /* Đường gạch chân nhẹ giữa các hàng */
}

.product-details .label {
  font-weight: bold;
  color: #495057;
  font-size: 16px;
}

.product-details .value {
  color: #000000;
  word-wrap: break-word; /* Tự động xuống dòng nếu quá dài */
  font-size: 16px;
}

/* Tạo nút với viền tròn hai bên */
.btn-custom-primary {
  background-color: #0078ff; /* Màu nền xanh */
  color: #fff; /* Màu chữ trắng */
  font-size: 18px; /* Cỡ chữ */
  font-weight: 500; /* Độ đậm chữ */
  border: none; /* Bỏ viền */
  padding: 12px 24px; /* Kích thước padding cho nút */
  text-transform: uppercase; /* Chữ in hoa */
  transition: background-color 0.3s ease, box-shadow 0.3s ease; /* Hiệu ứng chuyển động mượt */
  border-radius: 30px; /* Viền tròn hai bên */
}

.btn-custom-primary:hover {
  background-color: #005cbf; /* Màu xanh đậm khi hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Đổ bóng khi di chuột */
  color: #fff; /* Giữ màu chữ trắng khi hover */
}

.btn-custom-primary i {
  margin-right: 8px; /* Tạo khoảng cách giữa icon và chữ */
  font-size: 18px; /* Kích thước icon */
}

/* Menu bên trái */
.nav-link {
  color: #495057;
  padding: 10px 15px;
  font-size: 15px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background-color: #e9ecef;
  color: #000;
  font-weight: bold;
  border-radius: 6px;
}

/* Cột trái và phải căn chỉnh padding */
.border-end {
  border-right: 1px solid #e0e0e0;
}

.pe-4 {
  padding-right: 1.5rem; /* Khoảng cách bên phải menu */
}

.ps-4 {
  padding-left: 1.5rem; /* Khoảng cách bên trái nội dung */
}

/* Thay đổi kích thước chữ cho các liên kết trong cột bên trái */
.nav-link {
  font-size: 18px; /* Thay đổi kích thước chữ */
  font-weight: bold; /* Đặt chữ đậm */
}


/* Nội dung bên phải */
h5 {
  font-size: 18px;
  color: #495057;
}

p, li {
  font-size: 15px;
  line-height: 1.8;
  color: #000000;
}

ul {
  padding-left: 20px;
}

.card {
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.container-fluid {
  background-color: #fff;
  padding: 16px;
}

.copyable-text {
  cursor: pointer;
  color: #007bff;

  font-family: 'Poppins', sans-serif;
  transition: color 0.3s;
}

.copyable-text:hover {
  color: #0056b3;
}

.copy-message {
  margin-top: 5px;
  font-size: 14px;
  color: green;
  font-weight: bold;
  animation: fadeOut 2s forwards;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}


.text-truncate {
  display: inline-block;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card {
  border-radius: 16px;

}

.card-link {
  text-decoration: none; /* Loại bỏ gạch chân mặc định của thẻ <a> */
  display: block;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.card-link:hover .card {
  transform: scale(1.02); /* Phóng to nhẹ khi hover */
  box-shadow: 0px 4px 10px #3964e7; /* Hiệu ứng đổ bóng */
}

.card-link:active .card {
  transform: scale(0.98); /* Hiệu ứng bấm xuống */
}

.responsive-img {
  object-fit: contain;
  display: flex;
  justify-content: center; /* Căn giữa theo chiều ngang */
  align-items: center; /* Căn giữa theo chiều dọc */
  width: 100%; /* Chiều rộng khung chứa */
  height: 175px; /* Chiều cao khung chứa */
}

.text-container {
  display: flex;
  justify-content: center; /* Căn giữa theo chiều ngang */
  align-items: center; /* Căn giữa theo chiều dọc */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}