<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Chi tiết thuốc</h4>
          </div>
        </div>
        <!-- end page title -->

        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-body">
                <div class="row gx-lg-5">
                  <div class="col-xl-4 col-md-8 mx-auto">
                    <div class="product-img-slider sticky-side-div">
                      <div class="swiper product-nav-slider mt-2">
                        <div class="swiper-wrapper">
                          <div class="swiper-slide">
                            <div class="nav-slide-item">
                              <img
                                [src]="thuoc.avatar"
                                alt=""
                                class="img-fluid d-block"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- end swiper nav slide -->
                    </div>
                  </div>
                  <!-- end col -->

                  <div class="col-xl-8">
                    <div class="mt-xl-0 mt-5">
                      <div class="d-flex">
                        <div class="flex-grow-1">
                          <h3><br />{{ thuoc.tenThuoc }}</h3>
                        </div>
                        <div class="flex-shrink-0" *ngIf="isAdmin == true">
                          <div>
                            <a
                              [routerLink]="['/sys/product-create/', thuoc.id]"
                              class="btn btn-light"
                              data-bs-toggle="tooltip"
                              data-bs-placement="top"
                              title="Chỉnh sửa"
                              ><i class="ri-pencil-fill align-bottom"></i
                            ></a>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 (click)="copyToClipboard(thuoc.maThuoc)" class="copyable-text">
                          {{ thuoc.maThuoc }}
                        </h5>
                        <div *ngIf="showCopyMessage" class="copy-message">
                          Đã sao chép mã thuốc!
                        </div>
                      </div>


                      <div class="row mt-4">
                        <div class="price">
                          <span class="current-price">{{ thuoc.giaBan | number : "1.0-0"  }}đ / {{thuoc.donVi}}</span>
                        </div>

                        <ul class="product-details list-unstyled mt-4">
                          <li>
                            <div class="label">Loại thuốc:</div>
                            <div class="value">
                              {{ thuoc.loaiThuoc?.tenLoai }}
                            </div>
                          </li>
                          <li>
                            <div class="label">Dạng bào chế:</div>
                            <div class="value">{{ thuoc.cheBao }}</div>
                          </li>
                          <li>
                            <div class="label">Nhà sản xuất:</div>
                            <div class="value">
                              {{ thuoc.nhaSanXuat?.tenNhaSanXuat }}
                            </div>
                          </li>
                          <li>
                            <div class="label">Thành phần:</div>
                            <div class="value">
                              <span
                                *ngFor="
                                  let item of thuoc.thanhPhanThuocs;
                                  let last = last
                                "
                              >
                                {{ item.tenThanhPhan }} ({{ item.hamLuong }}
                                {{ item.donVi }}){{ last ? "" : "," }}
                              </span>
                            </div>
                          </li>
                          <li>
                            <div class="label">Mô tả ngắn:</div>
                            <div class="value">{{ thuoc.moTaNgan }}</div>
                          </li>
                        </ul>

                        <div class="d-flex justify-content-end">
                          <div class="flex-shrink-0 ms-2">
                            <div>
                              <a
                                class="btn btn-custom-primary"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                (click)="addProductInCart()"
                              >
                                <i
                                  class="ri-shopping-cart-fill align-bottom"
                                ></i>
                                Chọn mua
                              </a>
                            </div>
                          </div>
                        </div>


                      </div>
                    </div>
                    <!-- end col -->
                  </div>
                  <!-- end row -->
                </div>
                <!-- end card body -->
              </div>
              <!-- end card -->
            </div>
            <!-- end col -->
          </div>
          <!-- end row -->
        </div>

        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-body p-0">
                <div class="container-fluid mt-4 px-0">
                  <div class="row gx-0">


                    <!-- Cột bên phải: Nội dung động -->
                    <div class="col-md-10 p-0">
                      <!-- Mô tả sản phẩm -->
                      <div id="mo-ta-san-pham" class="mb-4">
                        <h5 class="fw-bold">Mô tả sản phẩm</h5>
                        <p>{{ thuoc.moTaNgan }}</p>
                      </div>

                      <!-- Thành phần -->
                      <div id="thanh-phan" class="mb-4">
                        <h5 class="fw-bold">Thành phần</h5>
                        <ul>
                          <li *ngFor="let tp of thuoc.thanhPhanThuocs">
                            {{ tp.tenThanhPhan }} - {{ tp.hamLuong }}
                            {{ tp.donVi }}
                          </li>
                        </ul>
                      </div>

                      <!-- Công dụng -->
                      <div id="cong-dung" class="mb-4">
                        <h5 class="fw-bold">Công dụng</h5>
                        <p>{{ thuoc.congDung }}</p>
                      </div>

                      <!-- Cách dùng -->
                      <div id="cach-dung" class="mb-4">
                        <h5 class="fw-bold">Cách dùng</h5>
                        <p>{{ thuoc.huongDanSuDung }}</p>
                      </div>

                      <!-- Tác dụng phụ -->
                      <div id="tac-dung-phu" class="mb-4">
                        <h5 class="fw-bold">Lưu ý</h5>
                        <p>{{ thuoc.ghiChu }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-12">
            <h4 class="mb-sm-0">Sản phẩm liên quan</h4>
            <!-- <div class="card">               -->
              <div class="card-body">
                <div class="row mb-4">
                  <div class="bestseller-section">
                    <div class="row mb-4">
                      <div class="col-xl-2 col-lg-6" *ngFor="let item of thuocTT | slice: 0:6">
                        <a (click)="showDetail(item)" class="card-link">
                          <div class="card ribbon-box right overflow-hidden">
                            <div class="card-body text-center p-4">
                              <img [src]="item.avatar" alt="" class="responsive-img" />
                              <div class="text-container">
                                <h5 class="mb-1 mt-4">
                                  <span class="link-primary text-truncate">
                                    {{ item.tenThuoc }}
                                  </span>
                                </h5>
                              </div>
                              <div class="row justify-content-center">
                                <div class="col-lg-8">
                                  <div id="chart-seller1" data-colors='["--vz-danger"]'></div>
                                </div>
                              </div>
                              <div class="row mt-4">
                                <div class="col-lg-12">
                                  <h5>
                                    {{ item.giaBan | number : "1.0-0" }}đ / {{ item.donVi }}
                                  </h5>
                                </div>
                              </div>
                              <div class="mt-4">
                                <button
                                  class="btn btn-custom-primary w-100"
                                  (click)="addProductInCart(item); $event.stopPropagation()"
                                >
                                  Mua
                                </button>
                              </div>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              <!-- </div> -->
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-body">
                <div class="">
                  <div>
                    <h5 class="fs-14 mb-3">Đánh giá</h5>
                  </div>
                  <div class="row gy-4 gx-0">
                    <div class="col-lg-12">
                      <div class="ps-lg-4">
                        <div class="d-flex flex-wrap align-items-start gap-3">
                          <!-- <h5 class="fs-14">Đánh giá:</h5> -->
                        </div>

                        <div class="me-lg-n3 pe-lg-4" data-simplebar>
                          <ul class="list-unstyled mb-0">
                            <li class="py-2" *ngFor="let item of danhgiaLst">
                              <div class="border border-dashed rounded p-3">
                                <div class="d-flex align-items-start mb-3">
                                  <div class="hstack gap-3">
                                    <div
                                      class="badge rounded-pill bg-success mb-0"
                                    >
                                      <i class="mdi mdi-star"></i>
                                      {{ item.diemSo }}
                                    </div>
                                    <div class="vr"></div>
                                    <div class="flex-grow-1">
                                      <p class="text-muted mb-0">
                                        {{ item.danhGia }}
                                      </p>
                                    </div>
                                  </div>
                                </div>

                                <div class="d-flex align-items-end">
                                  <div class="flex-grow-1">
                                    <h5 class="fs-14 mb-0">
                                      {{ item.nguoiDung?.hoTen }}
                                    </h5>
                                  </div>

                                  <div class="flex-shrink-0">
                                    <p class="text-muted fs-13 mb-0">
                                      {{
                                        item.createdAt
                                          | date : "dd/MM/yyyy HH:mm:ss"
                                      }}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <!-- end col -->
                  </div>
                  <!-- end Ratings & Reviews -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
