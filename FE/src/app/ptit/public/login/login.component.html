<div
  class="auth-page-wrapper auth-bg-cover py-5 d-flex justify-content-center align-items-center min-vh-100"
>
  <div class="bg-overlay"></div>
  <!-- auth-page content -->
  <div class="auth-page-content overflow-hidden pt-lg-5">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div class="card overflow-hidden">
            <div class="row g-0">
              <div class="col-lg-6">
                <div class="p-lg-5 p-4 auth-one-bg h-100">
                  <div class="bg-overlay"></div>
                  <div class="position-relative h-100 d-flex flex-column">
                    <div class="mb-4">
                      <a class="d-block">
                        <img
                          src="assets/images/logo.png"
                          alt=""
                          height="30"
                        />
                      </a>
                    </div>
                    <div class="mt-auto">
                      <div class="mb-3">
                        <i
                          class="ri-double-quotes-l display-4 text-success"
                        ></i>
                      </div>

                      <div
                        id="qoutescarouselIndicators"
                        class="carousel slide"
                        data-bs-ride="carousel"
                      >
                        <div class="carousel-indicators">
                          <button
                            type="button"
                            data-bs-target="#qoutescarouselIndicators"
                            data-bs-slide-to="0"
                            class="active"
                            aria-current="true"
                            aria-label="Slide 1"
                          ></button>
                          <button
                            type="button"
                            data-bs-target="#qoutescarouselIndicators"
                            data-bs-slide-to="1"
                            aria-label="Slide 2"
                          ></button>

                        </div>
                        <div class="carousel-inner text-center text-white pb-5">
                          <div class="carousel-item active">
                            <p class="fs-15 fst-italic">
                              " Thân thiện cam kết
                              chất lượng sản phẩm. "
                            </p>
                          </div>
                          <div class="carousel-item">
                            <p class="fs-15 fst-italic">
                              " Miễn phí vận chuyển
                              theo chính sách giao hàng."
                            </p>
                          </div>
                        </div>
                      </div>
                      <!-- end carousel -->
                    </div>
                  </div>
                </div>
              </div>
              <!-- end col -->

              <div class="col-lg-6">
                <div class="p-lg-5 p-4">
                  <div>
                    <h5 class="text-primary">Chào mừng bạn !</h5>
                    <p class="text-muted">
                      Đăng nhập để tiếp tục với hiệu thuốc.
                    </p>
                  </div>

                  <div class="mt-4">
                      <div class="mb-3">
                        <label for="username" class="form-label"
                          >Tên đăng nhập</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="username"
                          name="username"
                          placeholder="Nhập tên đăng nhập"
                          [(ngModel)]="user.tenDangNhap"
                        />
                      </div>

                      <div class="mb-3">
                        <div class="float-end">
                          <a class="text-muted">Quên mật khẩu?</a>
                        </div>
                        <label class="form-label" for="password-input"
                          >Mật khẩu</label
                        >
                        <div class="position-relative auth-pass-inputgroup">
                          <input
                            [type]="showPassword ? 'text' : 'password'"
                            class="form-control pe-5 password-input"
                            placeholder="Nhập mật khẩu"
                            id="password-input"
                            [(ngModel)]="user.matKhau"
                          />
                          <button
                            type="button"
                            class="btn btn-link position-absolute end-0 top-0 text-decoration-none text-muted password-addon"
                            (click)="togglePasswordVisibility()"
                          >
                            <i [class]="showPassword ? 'ri-eye-off-fill' : 'ri-eye-fill'"></i>
                          </button>
                          <div class="invalid-feedback">Hãy nhập mật khẩu</div>
                        </div>
                      </div>

                      <!-- <div class="form-check">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          value=""
                          id="auth-remember-check"
                        />
                        <label
                          class="form-check-label"
                          for="auth-remember-check"
                          >Remember me</label
                        >
                      </div> -->

                      <div class="mt-4">
                        <button
                          class="btn btn-success w-100"
                          type="submit"
                          (click)="login()"
                        >
                          Đăng nhập
                        </button>
                      </div>
                  </div>

                  <div class="mt-5 text-center">
                    <p class="mb-0">
                      Bạn chưa có tài khoản ?
                      <a
                        [routerLink]="['/signup']"
                        class="fw-semibold text-primary text-decoration-underline"
                      >
                        Đăng ký</a
                      >
                    </p>
                  </div>
                </div>
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
          <!-- end card -->
        </div>
        <!-- end col -->
      </div>
      <!-- end row -->
    </div>
    <!-- end container -->
  </div>
  <!-- end auth page content -->

  <!-- footer -->
  <footer class="footer">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div class="text-center">
            <p class="mb-0">
              &copy;
              <script>
                document.write(new Date().getFullYear());
              </script>
              Ptit. Crafted with <i class="mdi mdi-heart text-danger"></i> by
              Ptit
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!-- end Footer -->
</div>
