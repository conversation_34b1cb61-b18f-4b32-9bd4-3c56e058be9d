<div iv class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <!-- <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Thu<PERSON><PERSON></h4>
          </div>
        </div>
      </div> -->

      <!-- end page title -->

      <div class="search-container">
        <div class="card">
          <div class="card-header border-0 rounded">
            <div class="search-box-custom">
              <input type="text" class="form-control search-input" placeholder="T<PERSON><PERSON> tên thuốc, thực phẩm chức năng..."
                [(ngModel)]="modelSearch.keyWord" (keydown.enter)="search()" />
              <button type="button" class="search-button" (click)="search()">
                <i class="ri-search-line"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="content-container">
        <div class="row">
          <!-- Slider (2/3 chiều rộng) -->
          <div class="col-lg-8 col-md-8 col-sm-12">
            <div id="carouselExample" class="carousel slide" data-bs-ride="carousel">
              <div class="carousel-indicators">
                <button type="button" data-bs-target="#carouselExample" data-bs-slide-to="0" class="active"
                  aria-current="true" aria-label="Slide 1"></button>
                <button type="button" data-bs-target="#carouselExample" data-bs-slide-to="1"
                  aria-label="Slide 2"></button>
                <button type="button" data-bs-target="#carouselExample" data-bs-slide-to="2"
                  aria-label="Slide 3"></button>
                <button type="button" data-bs-target="#carouselExample" data-bs-slide-to="3"
                  aria-label="Slide 4"></button>
                <button type="button" data-bs-target="#carouselExample" data-bs-slide-to="44"
                  aria-label="Slide 5"></button>
              </div>

              <div class="carousel-inner">
                <div class="carousel-item active">
                  <img src="assets/images/slide home/4.webp" class="d-block w-100" alt="Slide 1" />
                </div>
                <div class="carousel-item">
                  <a [routerLink]="['/thuoc-chitiet', 31]">
                    <img src="assets/images/slide home/2.webp" class="d-block w-100" alt="Slide 2" />
                  </a>
                </div>
                <div class="carousel-item">
                  <img src="assets/images/slide home/1.webp" class="d-block w-100" alt="Slide 3" />
                </div>
                <div class="carousel-item">
                  <img src="assets/images/slide home/3.webp" class="d-block w-100" alt="Slide 4" />
                </div>
                <div class="carousel-item">
                  <img src="assets/images/slide home/5.webp" class="d-block w-100" alt="Slide 5" />
                </div>
              </div>

              <button class="carousel-control-prev" type="button" data-bs-target="#carouselExample"
                data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
              </button>
              <button class="carousel-control-next" type="button" data-bs-target="#carouselExample"
                data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
              </button>
            </div>
          </div>

          <!-- Hình ảnh bên phải (1/3 chiều rộng) -->
          <div class="col-lg-4 col-md-4 col-sm-12 d-flex flex-column justify-content-between">
            <img src="assets/images/slide home/benh&suckhoe.webp" alt="Right Image 1" class="right-image" />
            <img src="assets/images/slide home/sinhluc.jpg" alt="Right Image 2" class="right-image" />
          </div>
        </div>
      </div>

      <div class="row mb-4">
        <div class="bestseller-section">
          <div class="bestseller-title">
            <span>Sản phẩm bán chạy</span>
          </div>
          <div class="row mb-4">
            <div class="col-xl-2 col-lg-6" *ngFor="let item of productBS | slice: 0:6">
              <a (click)="showDetail(item)" class="card-link">
                <div class="card ribbon-box right overflow-hidden">
                  <div class="card-body text-center p-4">
                    <img [src]="item.avatar" alt="" class="responsive-img" />
                    <div class="text-container">
                      <h5 class="mb-1 mt-4">
                        <span class="link-primary text-truncate">
                          {{ item.tenThuoc }}
                        </span>
                      </h5>
                    </div>
                    <div class="row justify-content-center">
                      <div class="col-lg-8">
                        <div id="chart-seller1" data-colors='["--vz-danger"]'></div>
                      </div>
                    </div>
                    <div class="row mt-4">
                      <div class="col-lg-12">
                        <h5>
                          {{ item.giaBan | number : "1.0-0" }}đ / {{ item.donVi }}
                        </h5>
                      </div>
                    </div>
                    <div class="mt-4">
                      <button class="btn btn-custom-primary w-100"
                        (click)="addProductInCart(item); $event.stopPropagation()">
                        Mua
                      </button>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="row mb-4">
        <div class="bestseller-section">
          <div class="bestseller-title">
            <span>Sản phẩm</span>
          </div>
          <div class="row mb-4">
            <div *ngIf="paginatedProductLst.length === 0 && modelSearch.keyWord" class="col-12 text-center py-5">
              <div class="alert alert-info">
                <i class="ri-information-line me-2"></i>
                Không tìm thấy sản phẩm nào phù hợp với từ khóa "{{ modelSearch.keyWord }}".
              </div>
            </div>
            <div class="col-xl-2 col-lg-6" *ngFor="let item of paginatedProductLst">
              <a (click)="showDetail(item)" class="card-link">
                <div class="card ribbon-box right overflow-hidden">
                  <div class="card-body text-center p-4">
                    <img [src]="item.avatar" alt="" class="responsive-img" />
                    <div class="text-container">
                      <h5 class="mb-1 mt-4">
                        <span class="link-primary text-truncate">
                          {{ item.tenThuoc }}
                        </span>
                      </h5>
                    </div>
                    <div class="row justify-content-center">
                      <div class="col-lg-8">
                        <div id="chart-seller1" data-colors='["--vz-danger"]'></div>
                      </div>
                    </div>
                    <div class="row mt-4">
                      <div class="col-lg-12">
                        <h5>
                          {{ item.giaBan | number : "1.0-0" }}đ / {{ item.donVi }}
                        </h5>
                      </div>
                    </div>
                    <div class="mt-4">
                      <button class="btn btn-custom-primary w-100"
                        (click)="addProductInCart(item); $event.stopPropagation()">
                        Mua
                      </button>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
          <div class="align-items-center mt-2 row g-3 text-center text-sm-start">
            <div class="col-sm">
              <div class="text-muted">
                <!-- Showing<span class="fw-semibold">4</span> of
                <span class="fw-semibold">125</span> Results -->
              </div>
            </div>
            <div class="col-sm-auto">
              <ul
                class="pagination pagination-separated pagination-sm justify-content-center justify-content-sm-start mb-0">
                <!-- Previous Page -->
                <li class="page-item" [class.disabled]="currentPage === 1">
                  <a class="page-link" (click)="changePage(currentPage - 1)">←</a>
                </li>

                <!-- Page Numbers -->
                <li class="page-item" *ngFor="let page of pages" [class.active]="page === currentPage">
                  <a class="page-link" (click)="changePage(page)">{{ page }}</a>
                </li>

                <!-- Next Page -->
                <li class="page-item" [class.disabled]="currentPage === totalPage">
                  <a class="page-link" (click)="changePage(currentPage + 1)">→</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <!--end modal-->
    </div>
    <!-- container-fluid -->
  </div>
  <!-- End Page-content -->
</div>