<p-dialog
  header="Tìm khách hàng"
  [(visible)]="displayModal"
  [modal]="true"
  [style]="{ width: '50vw' }"
  [baseZIndex]="10000"
  [draggable]="true"
  [resizable]="false"
  [modal]="true"
  (onHide)="onCancel()"
>
  <div>
    <div class="mb-3 d-flex">
      <div class="d-flex flex-column col-xl-9">
        <label for="titleDropdown">Tên Kh<PERSON>ch Hàng </label>
        <input
          type="text"
          class="form-control"
          [(ngModel)]="modelSearch.keyWord"
        />
      </div>

      <div class="d-flex align-items-end col-xl-2 ms-2">
        <button
          type="button"
          class="btn btn-info shadow-none mt-3"
          (click)="search()"
        >
          <i class="ri-search-line align-middle me-1"></i>
          T<PERSON><PERSON>
        </button>
      </div>
    </div>

    <div class="mb-3">
      <label class="col-form-label" for="menuName"></label>

      <p-table
        [value]="customerLst"
        [rowHover]="true"
        dataKey="id"
        [showCurrentPageReport]="true"
        [rows]="10"
        [alwaysShowPaginator]="true"
        [paginator]="false"
        currentPageReportTemplate=""
      >
        <ng-template pTemplate="header">
          <tr>
            <th></th>
            <th>STT</th>
            <th>ID</th>
            <th>Tên</th>

            <th>Số điện thoại</th>
            <th></th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-model let-rowIndex="rowIndex">
          <tr [style]="{ cursor: 'pointer' }">
            <td>
              <input
                class="form-check-input"
                type="radio"
                name="selectScoreCard"
                [id]="model.id"
                (click)="selectKH(model)"
                [checked]="isSelected(model.id)"
              />
            </td>
            <td (click)="selectKH(model)">{{ rowIndex + 1 }}</td>
            <td (click)="selectKH(model)">{{ model.id }}</td>
            <td (click)="selectKH(model)">{{ model.hoTen }}</td>
            <td (click)="selectKH(model)">{{ model.soDienThoai }}</td>

            <td>
              <!-- <a
                class="text-danger d-inline-block remove-item-btn"
                data-bs-toggle="modal"
                (click)="deleteDoituong(model)"
              >
                <i class="ri-delete-bin-line fs-2"></i>
              </a> -->
              <!-- <i
                (click)="preDeleteScoreCard(scoreCard)"
                class="ri-delete-bin-line fs-2 me-2"
              ></i> -->
              <!-- <i class="ri-edit-line fs-2" (click)="update(model)"></i> -->
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <p-button
      icon="pi pi-times"
      (click)="displayModal = false"
      label="Hủy"
      styleClass="btn btn-danger"
    ></p-button>
    <p-button
      icon="pi pi-check"
      label="Lưu"
      (click)="onSave()"
      styleClass="btn btn-success"
    ></p-button>
  </ng-template>
</p-dialog>

<!-- Dialog mới chồng lên dialog hiện tại -->
