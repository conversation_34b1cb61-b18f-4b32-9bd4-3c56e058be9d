<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div
        *ngIf="displayDialog"
        id="layer-popup"
        [ngClass]="{ active: displayDialog }"
      >
        <app-ncc-createment
          (cancel)="handleCancel($event)"
          [ncc]="nccNew"
          (save)="handeSave($event)"
        >
        </app-ncc-createment>
      </div>

      <app-confirm-dialog-common> </app-confirm-dialog-common>

      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Nhà Cung Cấp</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- search -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="d-flex flex-column col-xl-9">
                  <label for="titleDropdown">Tên Nhà Cung Cấp </label>
                  <input
                    type="text"
                    pInputText
                    [(ngModel)]="modelSearch.keyWord"
                  />
                </div>

                <div class="d-flex align-items-end col-xl-2">
                  <button
                    type="button"
                    class="btn btn-info shadow-none mt-3"
                    (click)="search()"
                  >
                    <i class="ri-search-line align-middle me-1"></i>
                    Tìm Kiếm
                  </button>
                </div>
              </div>

              <!-- <div class="col-auto mb-3"></div> -->
            </div>
          </div>
        </div>
      </div>

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ nccLst.length }}</span>
                  </div>
                </div>
                <button
                  type="button"
                  class="btn btn-success shadow-none"
                  (click)="preAdd()"
                >
                  <i class="ri-add-circle-line align-middle me-1"></i>
                  Thêm nhà cung cấp
                </button>
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table
                  [value]="nccLst"
                  [rowHover]="true"
                  dataKey="id"
                  [showCurrentPageReport]="true"
                  [rows]="10"
                  [alwaysShowPaginator]="true"
                  [paginator]="true"
                  currentPageReportTemplate=""
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th>ID</th>
                      <th>Mã Nhà Cung Cấp</th>
                      <th>Tên Nhà Cung Cấp</th>
                      <th>Địa Chỉ</th>
                      <th>Số Điện Thoại</th>
                      <th>Email</th>
                      <th></th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-ncc let-rowIndex="rowIndex">
                    <tr [style]="{ cursor: 'pointer' }">
                      <td (click)="preUpdate(ncc)">{{ rowIndex + 1 }}</td>
                      <td (click)="preUpdate(ncc)">{{ ncc.maNCC }}</td>
                      <td (click)="preUpdate(ncc)">{{ ncc.tenNhaCungCap }}</td>
                      <td (click)="preUpdate(ncc)">{{ ncc.diaChi }}</td>
                      <td (click)="preUpdate(ncc)">{{ ncc.soDienThoai }}</td>
                      <td (click)="preUpdate(ncc)">{{ ncc.email }}</td>
                      <td>
                        <a
                          class="text-danger d-inline-block remove-item-btn"
                          data-bs-toggle="modal"
                          href="#delete"
                          (click)="preDelete(ncc)"
                        >
                          <i class="ri-delete-bin-line fs-2"></i>
                        </a>
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
