<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0"><PERSON><PERSON><PERSON>ng</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ donhangLst.length }}</span>
                  </div>
                </div>
                <!-- <button
                    type="button"
                    class="btn btn-success shadow-none"
                    [routerLink]="['/sys/product-create']"
                  >
                    <i class="ri-add-circle-line align-middle me-1"></i>
                    Thêm Khách Hàng
                  </button> -->
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table
                  [value]="donhangLst"
                  [rowHover]="true"
                  dataKey="id"
                  [showCurrentPageReport]="true"
                  [rows]="10"
                  [alwaysShowPaginator]="true"
                  [paginator]="true"
                  currentPageReportTemplate=""
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th>STT</th>
                      <!-- <th>ID Khách Hàng</th> -->
                      <th>Tên khách hàng</th>
                      <th>Số điện thoại</th>
                      <!-- <th>Địa Chỉ</th> -->
                      <th>Email</th>
                      <th>Ngày tao</th>
                      <th>Tổng tiền</th>
                      <th>Trạng Thái Giao Hàng</th>
                      <th>Ngày giao</th>
                    </tr>
                  </ng-template>
                  <ng-template
                    pTemplate="body"
                    let-order
                    let-rowIndex="rowIndex"
                  >
                    <tr [style]="{ cursor: 'pointer' }">
                      <td (click)="preUpdate(order)">{{ rowIndex + 1 }}</td>
                      <!-- <td>{{ order.khachHang.id }}</td> -->
                      <td (click)="preUpdate(order)">
                        {{ order.tenKhachHang }}
                      </td>
                      <td (click)="preUpdate(order)">
                        {{ order.soDienThoai }}
                      </td>
                      <!-- <td>{{ order.diaChi }}</td> -->
                      <td (click)="preUpdate(order)">{{ order.email }}</td>
                      <td (click)="preUpdate(order)">
                        {{ order.createdAt | date : "dd-MM-yyyy" }}
                      </td>
                      <td (click)="preUpdate(order)">
                        {{ order.tongTien | currency : "VND" : "symbol" }}
                      </td>
                      <td (click)="preUpdate(order)">
                        <span
                          class="badge bg-warning"
                          *ngIf="
                            order.trangThaiGiaoHang ==
                            TrangThaiGiaoHang.DANG_XU_LY
                          "
                        >
                          Đang xử lý
                        </span>

                        <span
                          class="badge bg-danger"
                          *ngIf="
                            order.trangThaiGiaoHang == TrangThaiGiaoHang.DA_HUY
                          "
                        >
                          Đã hủy
                        </span>

                        <span
                          class="badge bg-info"
                          *ngIf="
                            order.trangThaiGiaoHang ==
                            TrangThaiGiaoHang.DANG_GIAO
                          "
                        >
                          Đang giao
                        </span>

                        <span
                          class="badge bg-success"
                          *ngIf="
                            order.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                          "
                        >
                          Đã giao
                        </span>

                        <span
                          class="badge bg-primary"
                          *ngIf="
                            order.trangThaiGiaoHang ==
                            TrangThaiGiaoHang.TRA_HANG
                          "
                        >
                          Trả hàng
                        </span>
                      </td>
                      <td (click)="preUpdate(order)">
                        {{ order.ngayGiao | date : "dd-MM-yyyy" }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
