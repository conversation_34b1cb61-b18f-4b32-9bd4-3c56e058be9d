<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div
        *ngIf="displayDialog"
        id="layer-popup"
        [ngClass]="{ active: displayDialog }"
      >
        <app-chucnang-createment
          (cancel)="handleCancel($event)"
          [chucnang]="chucNangNew"
          (save)="handeSave($event)"
        >
        </app-chucnang-createment>
      </div>

      <app-confirm-dialog-common> </app-confirm-dialog-common>
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Chức năng</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <div class="row">
        <div class="col-xl-12 col-lg-12">
          <div>
            <div class="card">
              <div class="card-header border-0">
                <div class="row g-4">
                  <div class="col-sm-auto">
                    <!-- <div>
                      <a
                        href="apps-ecommerce-add-product.html"
                        class="btn btn-success"
                        id="addproduct-btn"
                        ><i class="ri-add-line align-bottom me-1"></i> Add
                        Product</a
                      >
                    </div> -->
                  </div>
                  <div class="col-sm">
                    <!-- <div class="d-flex justify-content-sm-end">
                      <div class="search-box ms-2">
                        <input
                          type="text"
                          class="form-control"
                          id="searchProductList"
                          placeholder="Search Products..."
                        />
                        <i class="ri-search-line search-icon"></i>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>

              <div class="card-header">
                <div class="row align-items-center">
                  <div class="col">
                    <ul
                      class="nav nav-tabs-custom card-header-tabs border-bottom-0"
                      role="tablist"
                    >
                      <!-- <li class="nav-item">
                        <a
                          class="nav-link active fw-semibold"
                          data-bs-toggle="tab"
                          href="#productnav-all"
                          role="tab"
                        >
                          Tất cả
                          <span
                            class="badge badge-soft-danger align-middle rounded-pill ms-1"
                            >{{ chucNangLst.length }}</span
                          >
                        </a>
                      </li> -->
                      <li class="nav-item">
                        <a
                          class="nav-link fw-semibold active"
                          data-bs-toggle="tab"
                          href="#productnav-published"
                          role="tab"
                          (click)="
                            getChucNangByQuyen(AuthConstant.ROLE_KHACHHANG)
                          "
                        >
                          Người dùng
                        </a>
                      </li>
                      <li class="nav-item">
                        <a
                          class="nav-link fw-semibold"
                          data-bs-toggle="tab"
                          href="#productnav-draft"
                          (click)="getChucNangByQuyen(AuthConstant.ROLE_ADMIN)"
                          role="tab"
                        >
                          Quản trị viên
                        </a>
                      </li>
                    </ul>
                  </div>
                  <div class="col-auto">
                    <div id="">
                      <div class="my-n1 d-flex align-items-center text-muted">
                        <button
                          type="button"
                          class="btn btn-success shadow-none"
                          (click)="confirmUpdateNhomQuyen()"
                        >
                          <i class="ri-add-circle-line align-middle me-1"></i>
                          Cập nhật
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- end card header -->
              <div class="row m-2">
                <div class="col-xl-12">
                  <div
                    class="align-items-center p-3 justify-content-between d-flex"
                  ></div>
                  <div class="col-auto mb-3"></div>

                  <div class="col-auto" *ngIf="chucNangLst.length > 0">
                    <p-table
                      [value]="chucNangLst"
                      [rowHover]="true"
                      dataKey="id"
                      [showCurrentPageReport]="true"
                      [rows]="10"
                      [alwaysShowPaginator]="true"
                      [paginator]="true"
                      currentPageReportTemplate=""
                    >
                      <ng-template pTemplate="header">
                        <tr>
                          <!-- <th>STT</th> -->
                          <th></th>
                          <th>Tên</th>

                          <th>Mô tả</th>
                          <th>Hành động</th>
                          <th></th>
                        </tr>
                      </ng-template>
                      <ng-template
                        pTemplate="body"
                        let-model
                        let-rowIndex="rowIndex"
                      >
                        <tr [style]="{ cursor: 'pointer' }">
                          <td (click)="updateNhomQuyen(model)">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="checkbox"
                                [value]="model.id"
                                id="productBrandRadio5"
                                [checked]="isChecked(model.id)"
                              />
                            </div>
                          </td>
                          <td (click)="updateNhomQuyen(model)">
                            {{ model.tenChucNang }}
                          </td>
                          <td (click)="updateNhomQuyen(model)">
                            {{ model.moTa }}
                          </td>
                          <td (click)="updateNhomQuyen(model)">
                            {{ model.hanhDong }}
                          </td>
                          <td>
                            <a
                              class="text-info d-inline-block remove-item-btn"
                              data-bs-toggle="modal"
                              href="#delete"
                              (click)="preUpdate(model)"
                            >
                              <i class="ri-edit-line fs-2"></i>
                            </a>
                          </td>
                        </tr>
                      </ng-template>
                    </p-table>
                  </div>

                  <!-- end tab pane -->

                  <!-- end tab pane -->

                  <div
                    class="tab-pane"
                    id="productnav-draft"
                    role="tabpanel"
                    *ngIf="chucNangLst.length == 0"
                  >
                    <div class="py-4 text-center">
                      <lord-icon
                        src="https://cdn.lordicon.com/msoeawqm.json"
                        trigger="loop"
                        colors="primary:#405189,secondary:#0ab39c"
                        style="width: 72px; height: 72px"
                      >
                      </lord-icon>
                      <h5 class="mt-4">Sorry! No Result Found</h5>
                    </div>
                  </div>
                  <!-- end tab pane -->
                </div>
                <!-- end tab content -->
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
          <!-- container-fluid -->
        </div>
        <!-- End Page-content -->
      </div>
      <!-- end main content-->
    </div>
  </div>
</div>
