<p-dialog
  [header]="
    typeModal == CommonConstant.TYPE_VIEW
      ? 'Xem chức năng'
      : 'Chỉnh sửa chức năng'
  "
  [(visible)]="displayModal"
  [modal]="true"
  [style]="{ width: '50vw' }"
  [baseZIndex]="10000"
  [draggable]="true"
  [resizable]="false"
  [modal]="true"
  (onHide)="onCancel()"
>
  <div>
    <div class="flex-shrink-0">
      <!-- Nút Cập nhật -->
      <button
        *ngIf="typeModal == CommonConstant.TYPE_UPDATE"
        type="button"
        class="btn btn-success shadow-none"
        (click)="updateTypeModal()"
      >
        <i class="ri-edit-line align-middle me-1"></i>
        Xem
      </button>

      <!-- Nút Xem -->
      <button
        *ngIf="typeModal != CommonConstant.TYPE_UPDATE"
        type="button"
        class="btn btn-primary shadow-none"
        (click)="updateTypeModal()"
      >
        <i class="ri-eye-line align-middle me-1"></i>
        Cậ<PERSON> nhật
      </button>
    </div>

    <div class="mb-3">
      <label class="col-form-label" for="title">
        Tên <span class="text-danger">*</span></label
      >
      <input
        type="text"
        name="title"
        class="form-control"
        id="title"
        placeholder="Nhập tên danh mục thuốc"
        #title="ngModel"
        [(ngModel)]="chucnang.tenChucNang"
        [disabled]="typeModal == CommonConstant.TYPE_VIEW"
        required
      />
    </div>
    <div
      class="text-danger"
      *ngIf="
        chucnang.tenChucNang == '' ||
        (title.invalid && (title.dirty || title.touched))
      "
    >
      Hãy nhập tên danh mục thuốc
    </div>

    <div class="mb-3">
      <label class="col-form-label" for="title"> Mô tả </label>
      <textarea
        type="text"
        name="mota"
        class="form-control"
        id="mota"
        placeholder="Nhập mô tả loại thuốc"
        [(ngModel)]="chucnang.moTa"
        [disabled]="typeModal == CommonConstant.TYPE_VIEW"
      >
      </textarea>
    </div>

    <div class="mb-3">
      <label class="col-form-label" for="title"> Hành động</label>
      <textarea
        type="text"
        name="mota"
        class="form-control"
        id="mota"
        placeholder="Nhập mô tả loại thuốc"
        [disabled]="typeModal == CommonConstant.TYPE_VIEW"
        [(ngModel)]="chucnang.hanhDong"
      >
      </textarea>
    </div>
  </div>
  <!-- 
  <ng-template pTemplate="footer">
    <p-button
      icon="pi pi-times"
      (click)="displayModal = false"
      label="Hủy"
      styleClass="btn btn-danger"
    ></p-button>
    <p-button
      icon="pi pi-check"
      label="Lưu"
      (click)="onSave()"
      styleClass="btn btn-success"
    ></p-button>
  </ng-template> -->
</p-dialog>
