<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Khách Hàng</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- search -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="d-flex flex-column col-xl-9">
                  <label for="titleDropdown">Tên Khách Hàng </label>
                  <input
                    type="text"
                    pInputText
                    [(ngModel)]="modelSearch.keyWord"
                  />
                </div>

                <div class="d-flex align-items-end col-xl-2">
                  <button
                    type="button"
                    class="btn btn-info shadow-none mt-3"
                    (click)="search()"
                  >
                    <i class="ri-search-line align-middle me-1"></i>
                    Tìm Kiếm
                  </button>
                </div>
              </div>

              <!-- <div class="col-auto mb-3"></div> -->
            </div>
          </div>
        </div>
      </div>

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ customerLst.length }}</span>
                  </div>
                </div>
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table
                  [value]="customerLst"
                  [rowHover]="true"
                  dataKey="id"
                  [showCurrentPageReport]="true"
                  [rows]="10"
                  [alwaysShowPaginator]="true"
                  [paginator]="true"
                  currentPageReportTemplate=""
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th>ID</th>
                      <th>Tên Đăng Nhập</th>
                      <th>Họ và Tên</th>
                      <th>Email</th>
                      <th>Số Điện Thoại</th>
                      <th>Trạng Thái</th>
                      <th>Ngày Tạo</th>
                    </tr>
                  </ng-template>
                  <ng-template
                    pTemplate="body"
                    let-customer
                    let-rowIndex="rowIndex"
                  >
                    <tr [style]="{ cursor: 'pointer' }">
                      <td>{{ customer.id }}</td>
                      <td>{{ customer.tenDangNhap }}</td>
                      <td>{{ customer.hoTen }}</td>
                      <td>{{ customer.email }}</td>
                      <td>{{ customer.soDienThoai }}</td>
                      <td>
                        <span
                          class="badge btn-success"
                          *ngIf="customer.trangThai == true"
                        >
                          Hoạt động
                        </span>
                        <span
                          class="badge btn-success"
                          *ngIf="customer.trangThai == false"
                        >
                          Không hoạt động
                        </span>
                      </td>
                      <td>{{ customer.createdAt | date : "dd/MM/yyyy" }}</td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
