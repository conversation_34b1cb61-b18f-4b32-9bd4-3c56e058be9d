<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <app-confirm-dialog-common> </app-confirm-dialog-common>

      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0"><PERSON><PERSON><PERSON> nhập</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- search -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-header border-0 align-items-center justify-content-end">
              <div class="align-items-center p-3 justify-content-between d-flex">
                <div class="d-flex flex-column col-xl-9">
                  <label for="titleDropdown">Tên ng<PERSON><PERSON><PERSON> nhâp </label>
                  <input type="text" pInputText [(ngModel)]="modelSearch.keyWord" />
                </div>

                <div class="d-flex align-items-end col-xl-2">
                  <button type="button" class="btn btn-info shadow-none mt-3" (click)="search()">
                    <i class="ri-search-line align-middle me-1"></i>
                    Tìm Kiếm
                  </button>
                </div>
              </div>

              <!-- <div class="col-auto mb-3"></div> -->
            </div>
          </div>
        </div>
      </div>

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-header border-0 align-items-center justify-content-end">
              <div class="align-items-center p-3 justify-content-between d-flex">
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ phieunhapLst.length }}</span>
                  </div>
                </div>
                <button type="button" class="btn btn-success shadow-none" [routerLink]="['/sys/phieunhap-create']">
                  <i class="ri-add-circle-line align-middle me-1"></i>
                  Thêm phiếu nhập
                </button>
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table [value]="phieunhapLst" [rowHover]="true" dataKey="id" [showCurrentPageReport]="true"
                  [rows]="10" [alwaysShowPaginator]="true" [paginator]="true" currentPageReportTemplate="">
                  <ng-template pTemplate="header">
                    <tr>
                      <th>ID</th>
                      <th>Nhà cung cấp</th>
                      <th>Người nhập</th>
                      <th>Ngày nhập</th>
                      <th>Tổng tiền</th>
                      <th></th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-model let-rowIndex="rowIndex">
                    <tr [style]="{ cursor: 'pointer' }">
                      <td (click)="preUpdate(model)">{{ rowIndex + 1 }}</td>
                      <td (click)="preUpdate(model)">
                        {{ model.nhaCungCap.tenNhaCungCap }}
                      </td>
                      <td (click)="preUpdate(model)">
                        {{ model.nguoiDung.hoTen }}
                      </td>
                      <td (click)="preUpdate(model)">
                        {{ model.createdAt | date: 'dd-MM-yyyy' }}
                      </td>
                      <td (click)="preUpdate(model)">
                        {{ model.tongTien }}
                      </td>
                      <td>
                        <a class="text-danger d-inline-block remove-item-btn" data-bs-toggle="modal" href="#delete"
                          (click)="preDelete(model)">
                          <i class="ri-delete-bin-line fs-2"></i>
                        </a>
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>