{"name": "ptit-front", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^14.1.0", "@angular/common": "^14.1.0", "@angular/compiler": "^14.1.0", "@angular/core": "^14.1.0", "@angular/forms": "^14.1.0", "@angular/material": "^10.2.4", "@angular/platform-browser": "^14.1.0", "@angular/platform-browser-dynamic": "^14.1.0", "@angular/router": "^14.1.0", "@angular/service-worker": "^14.3.0", "@ckeditor/ckeditor5-angular": "^7.0.0", "@ckeditor/ckeditor5-build-classic": "^39.0.2", "@ckeditor/ckeditor5-core": "^39.0.2", "@ckeditor/ckeditor5-engine": "^39.0.2", "@ckeditor/ckeditor5-paste-from-office": "^39.0.2", "@ckeditor/ckeditor5-utils": "^39.0.2", "@ckeditor/ckeditor5-watchdog": "^39.0.2", "@lordicon/element": "^1.6.0", "@ng-bootstrap/ng-bootstrap": "^13.1.1", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@types/file-saver": "^2.0.7", "ang-music-player": "^0.0.4", "axios": "^1.7.7", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.10.5", "docx": "^9.0.2", "eventsource": "^2.0.2", "file-saver": "^2.0.5", "firebase": "^10.1.0", "highcharts": "^12.1.0", "highcharts-angular": "^4.0.1", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "lottie-web": "^5.12.2", "ng2-cookies": "^1.0.12", "ngx-pagination": "^5.0.0", "ngx-toastr": "^15.2.2", "ngx-translate-messageformat-compiler": "^4.11.0", "popper.js": "^1.16.1", "primeicons": "^4.0.0", "primeng": "^14.2.3", "rxjs": "~7.5.0", "sweetalert2": "^11.7.18", "tslib": "^1.14.1", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.1.0", "@angular/cli": "~14.1.0", "@angular/compiler-cli": "^14.1.0", "@types/jasmine": "~4.0.0", "@types/jquery": "^3.5.30", "jasmine-core": "~4.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2"}}